# Supermarket Price Comparison Scraper

A comprehensive web scraping solution for comparing supermarket prices across multiple retailers. Built with Scrapy for robust scraping and FastAPI for the web interface.

## 🚀 Features

- **Dynamic Site Management**: Add/remove supermarket websites through web interface
- **Comprehensive Price Tracking**: Regular prices, discounts, and promotional offers
- **Price Comparison Engine**: Find the best deals across all tracked supermarkets
- **Automated Scheduling**: Regular scraping to keep price data current
- **Local Network Deployment**: CORS-configured for local network access
- **Data Persistence**: SQLite/PostgreSQL database with automatic cleanup
- **Real-time Monitoring**: Track scraping performance and system health
- **RESTful API**: Complete API for integration with other systems

## 🏗️ Architecture

The system consists of several components:

- **FastAPI Web Application**: REST API and web interface
- **Scrapy Framework**: Robust web scraping with built-in error handling
- **Celery Task Queue**: Background job processing and scheduling
- **Redis**: Message broker and caching
- **SQLite/PostgreSQL**: Data persistence
- **Price Comparison Engine**: Advanced algorithms for finding best deals

## 📋 Quick Start

### Option 1: Automated Setup (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd supermarket-scraper

# Run the automated setup script
chmod +x scripts/start_local.sh
./scripts/start_local.sh
```

### Option 2: Manual Setup

1. **Setup Environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration settings
   ```

3. **Initialize Database**
   ```bash
   alembic upgrade head
   ```

4. **Start Redis (if not using Docker)**
   ```bash
   redis-server --daemonize yes
   ```

5. **Start Services**
   ```bash
   # Terminal 1: API Server
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

   # Terminal 2: Celery Worker
   celery -A app.celery_app worker --loglevel=info

   # Terminal 3: Celery Beat Scheduler
   celery -A app.celery_app beat --loglevel=info

   # Terminal 4: Flower Monitoring (optional)
   celery -A app.celery_app flower --port=5555
   ```

### Option 3: Docker Deployment

```bash
# For local network access
chmod +x scripts/deploy_local_network.sh
./scripts/deploy_local_network.sh

# Or manually with Docker Compose
docker-compose up -d
docker-compose exec api alembic upgrade head
```

## 🌐 Access Points

After starting the services:

- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Alternative API Docs**: http://localhost:8000/redoc
- **Flower Monitoring**: http://localhost:5555
- **Health Check**: http://localhost:8000/health

## 📁 Project Structure

```
├── app/                    # FastAPI application
│   ├── api/               # API endpoints (supermarkets, products, scraping)
│   ├── core/              # Core configuration and settings
│   ├── db/                # Database models and operations
│   ├── services/          # Business logic (price comparison, monitoring)
│   └── tasks/             # Celery background tasks
├── scraper/               # Scrapy project
│   ├── supermarket_scraper/
│   │   ├── spiders/       # Spider implementations
│   │   ├── items.py       # Data models for scraped items
│   │   ├── pipelines.py   # Data processing pipelines
│   │   ├── middlewares.py # Custom middlewares
│   │   └── settings.py    # Scrapy configuration
│   └── scrapy.cfg         # Scrapy project configuration
├── static/                # Static web assets (CSS, JavaScript)
├── docs/                  # Documentation
├── tests/                 # Test suite
├── scripts/               # Deployment and utility scripts
├── alembic/               # Database migrations
└── logs/                  # Application logs
```

## 🛠️ Usage

### Adding New Supermarket Sites

1. **Via Web Interface**:
   - Navigate to http://localhost:8000
   - Go to "Supermarkets" section
   - Click "Add Supermarket"
   - Fill in the details and save

2. **Via API**:
   ```bash
   curl -X POST "http://localhost:8000/api/v1/supermarkets/" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Example Store",
       "domain": "example.com",
       "base_url": "https://example.com",
       "spider_name": "example_spider",
       "is_active": true
     }'
   ```

3. **Create Custom Spider**:
   See [docs/adding-sites.md](docs/adding-sites.md) for detailed instructions.

### Price Comparison

```bash
# Compare prices for a product
curl "http://localhost:8000/api/v1/products/compare?search=milk"

# Get best deals
curl "http://localhost:8000/api/v1/products/discounts?limit=10"

# Get price history
curl "http://localhost:8000/api/v1/products/1/price-history"
```

### Monitoring

```bash
# Check system health
curl "http://localhost:8000/health"

# View scraping logs
curl "http://localhost:8000/api/v1/scraping/logs"

# Get scraping statistics
curl "http://localhost:8000/api/v1/scraping/stats"
```

## 📚 Documentation

- **[Adding New Sites](docs/adding-sites.md)**: Complete guide for adding new supermarket websites
- **[API Documentation](docs/api-documentation.md)**: Detailed API reference
- **[Deployment Guide](docs/deployment-guide.md)**: Production deployment instructions

## 🧪 Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run all tests
pytest

# Run specific test categories
pytest tests/test_api.py -v
pytest tests/test_spiders.py -v

# Run with coverage
pytest --cov=app --cov=scraper
```

## 🔧 Configuration

Key configuration options in `.env`:

```bash
# Database
DATABASE_URL=sqlite:///./supermarket_scraper.db

# Redis
REDIS_URL=redis://localhost:6379/0

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
ALLOWED_ORIGINS=["http://localhost:3000", "http://192.168.*.*"]

# Scraping Settings
DOWNLOAD_DELAY=1
CONCURRENT_REQUESTS=16
SCRAPING_SCHEDULE_HOURS=6
```

## 🚀 Production Deployment

For production deployment:

1. Use PostgreSQL instead of SQLite
2. Set up proper monitoring and logging
3. Configure reverse proxy (nginx)
4. Use Docker for containerization
5. Set up automated backups

See [docs/deployment-guide.md](docs/deployment-guide.md) for detailed instructions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

If you encounter issues:

1. Check the [documentation](docs/)
2. Review the logs in the `logs/` directory
3. Test individual components
4. Check system requirements
5. Open an issue with detailed information

## 🎯 Roadmap

- [ ] Machine learning for price prediction
- [ ] Mobile app interface
- [ ] Advanced analytics dashboard
- [ ] Multi-language support
- [ ] Integration with shopping lists
- [ ] Price alert notifications
