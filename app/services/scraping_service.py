"""
Service for managing Scrapy spiders and scraping tasks.
"""
import os
import subprocess
import logging
from typing import Dict, List, Optional
from datetime import datetime, timezone
from sqlalchemy.orm import Session

from app.db.database import get_db
from app.db.models import Supermarket
from app.tasks.scraping_tasks import run_spider

logger = logging.getLogger(__name__)


class ScrapingService:
    """Service to manage scraping operations using Celery."""

    def __init__(self):
        self.active_tasks: Dict[str, Dict] = {}

    def start_scraping(self, spider_name: str, supermarket_id: int = None, **kwargs) -> str:
        """
        Start a scraping task for the specified spider using Celery.

        Args:
            spider_name: Name of the spider to run
            supermarket_id: ID of the supermarket (optional, will be looked up if not provided)
            **kwargs: Additional arguments to pass to the spider

        Returns:
            Task ID for tracking the scraping job
        """
        try:
            # Get supermarket ID if not provided
            if supermarket_id is None:
                from app.db.database import SessionLocal
                db = SessionLocal()
                try:
                    supermarket = db.query(Supermarket).filter(Supermarket.spider_name == spider_name).first()
                    if not supermarket:
                        logger.error(f"No supermarket found for spider {spider_name}")
                        raise ValueError(f"No supermarket found for spider {spider_name}")
                    supermarket_id = supermarket.id
                finally:
                    db.close()

            # Start Celery task
            task = run_spider.delay(
                spider_name=spider_name,
                supermarket_id=supermarket_id,
                **kwargs
            )

            # Store task information
            self.active_tasks[task.id] = {
                "spider_name": spider_name,
                "supermarket_id": supermarket_id,
                "started_at": datetime.now(timezone.utc),
                "status": "pending",
                "celery_task_id": task.id
            }

            logger.info(f"Started Celery scraping task {task.id} for spider {spider_name}")
            return task.id

        except Exception as e:
            logger.error(f"Failed to start scraping for spider {spider_name}: {e}")
            raise
    
    def stop_scraping(self, task_id: str) -> bool:
        """
        Stop a specific scraping task.
        
        Args:
            task_id: ID of the task to stop
        
        Returns:
            True if task was stopped, False if task not found
        """
        if task_id not in self.active_tasks:
            logger.warning(f"Task {task_id} not found in active tasks")
            return False
        
        task_info = self.active_tasks[task_id]
        celery_task_id = task_info.get("celery_task_id")

        if celery_task_id:
            try:
                # Revoke the Celery task
                from app.celery_app import celery_app
                celery_app.control.revoke(celery_task_id, terminate=True)

                logger.info(f"Stopped Celery scraping task {task_id}")

            except Exception as e:
                logger.error(f"Error stopping Celery task {task_id}: {e}")
                return False

        # Update task status
        task_info["status"] = "stopped"
        task_info["stopped_at"] = datetime.now(timezone.utc)

        return True
    
    def stop_spider(self, spider_name: str) -> int:
        """
        Stop all tasks for a specific spider.
        
        Args:
            spider_name: Name of the spider
        
        Returns:
            Number of tasks stopped
        """
        stopped_count = 0
        tasks_to_stop = [
            task_id for task_id, task in self.active_tasks.items()
            if task["spider_name"] == spider_name and task["status"] == "running"
        ]
        
        for task_id in tasks_to_stop:
            if self.stop_scraping(task_id):
                stopped_count += 1
        
        logger.info(f"Stopped {stopped_count} tasks for spider {spider_name}")
        return stopped_count
    
    def stop_all_scraping(self) -> int:
        """
        Stop all active scraping tasks.
        
        Returns:
            Number of tasks stopped
        """
        stopped_count = 0
        running_tasks = [
            task_id for task_id, task in self.active_tasks.items()
            if task["status"] == "running"
        ]
        
        for task_id in running_tasks:
            if self.stop_scraping(task_id):
                stopped_count += 1
        
        logger.info(f"Stopped {stopped_count} scraping tasks")
        return stopped_count
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """
        Get status of a specific task.
        
        Args:
            task_id: ID of the task
        
        Returns:
            Task status information or None if not found
        """
        if task_id not in self.active_tasks:
            return None
        
        task = self.active_tasks[task_id]
        process = task["process"]
        
        # Update status based on process state
        if process.poll() is None:
            # Process is still running
            task["status"] = "running"
        else:
            # Process has finished
            if task["status"] == "running":
                task["status"] = "completed" if process.returncode == 0 else "failed"
                task["completed_at"] = datetime.now(timezone.utc)
                task["return_code"] = process.returncode
        
        return {
            "task_id": task_id,
            "spider_name": task["spider_name"],
            "status": task["status"],
            "started_at": task["started_at"].isoformat(),
            "completed_at": task.get("completed_at", {}).isoformat() if task.get("completed_at") else None,
            "pid": task.get("pid"),
            "return_code": task.get("return_code")
        }
    
    def get_scraping_status(self) -> Dict:
        """
        Get overall scraping status.
        
        Returns:
            Dictionary with scraping system status
        """
        # Update all task statuses
        for task_id in list(self.active_tasks.keys()):
            self.get_task_status(task_id)
        
        active_tasks = len([
            task for task in self.active_tasks.values()
            if task["status"] == "running"
        ])
        
        running_spiders = list(set([
            task["spider_name"] for task in self.active_tasks.values()
            if task["status"] == "running"
        ]))
        
        return {
            "active_tasks": active_tasks,
            "pending_tasks": 0,  # Would need a queue system for this
            "running_spiders": running_spiders,
            "system_status": "healthy" if active_tasks < 10 else "busy",
            "total_tasks": len(self.active_tasks)
        }
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """
        Clean up old completed tasks from memory.
        
        Args:
            max_age_hours: Maximum age in hours for keeping completed tasks
        """
        cutoff_time = datetime.now(timezone.utc).timestamp() - (max_age_hours * 3600)
        
        tasks_to_remove = []
        for task_id, task in self.active_tasks.items():
            if task["status"] in ["completed", "failed", "stopped"]:
                completed_at = task.get("completed_at")
                if completed_at and completed_at.timestamp() < cutoff_time:
                    tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.active_tasks[task_id]
        
        if tasks_to_remove:
            logger.info(f"Cleaned up {len(tasks_to_remove)} old completed tasks")
    
    def list_available_spiders(self) -> List[str]:
        """
        Get list of available spiders.
        
        Returns:
            List of spider names
        """
        try:
            result = subprocess.run(
                ["scrapy", "list"],
                cwd=self.scrapy_project_path,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                spiders = [line.strip() for line in result.stdout.split('\n') if line.strip()]
                return spiders
            else:
                logger.error(f"Failed to list spiders: {result.stderr}")
                return []
                
        except Exception as e:
            logger.error(f"Error listing spiders: {e}")
            return []
    
    def validate_spider(self, spider_name: str) -> bool:
        """
        Validate that a spider exists.
        
        Args:
            spider_name: Name of the spider to validate
        
        Returns:
            True if spider exists, False otherwise
        """
        available_spiders = self.list_available_spiders()
        return spider_name in available_spiders
    
    def get_spider_info(self, spider_name: str) -> Optional[Dict]:
        """
        Get information about a specific spider.
        
        Args:
            spider_name: Name of the spider
        
        Returns:
            Spider information or None if not found
        """
        if not self.validate_spider(spider_name):
            return None
        
        # This could be extended to parse spider files and extract more info
        return {
            "name": spider_name,
            "available": True,
            "last_run": None,  # Would need to query database for this
            "status": "available"
        }
