"""
Service for monitoring system health and performance.
"""
import logging
import psutil
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional
from sqlalchemy.orm import sessionmaker
from sqlalchemy import func, and_, or_

from app.db.database import engine
from app.db.models import Supermarket, Product, Price, Discount, ScrapingLog
from app.celery_app import celery_app

logger = logging.getLogger(__name__)
Session = sessionmaker(bind=engine)


class MonitoringService:
    """Service for system monitoring and health checks."""
    
    def __init__(self):
        self.session = Session()
    
    def __del__(self):
        if hasattr(self, 'session'):
            self.session.close()
    
    def get_system_health(self) -> Dict:
        """
        Get comprehensive system health status.
        
        Returns:
            Dictionary with system health information
        """
        health_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "overall_status": "unknown",
            "components": {
                "database": self._check_database_health(),
                "celery": self._check_celery_health(),
                "scraping": self._check_scraping_health(),
                "storage": self._check_storage_health(),
                "memory": self._check_memory_usage()
            },
            "metrics": self._get_performance_metrics()
        }
        
        # Determine overall status
        component_statuses = [comp["status"] for comp in health_data["components"].values()]
        
        if all(status == "healthy" for status in component_statuses):
            health_data["overall_status"] = "healthy"
        elif any(status == "critical" for status in component_statuses):
            health_data["overall_status"] = "critical"
        elif any(status == "warning" for status in component_statuses):
            health_data["overall_status"] = "warning"
        else:
            health_data["overall_status"] = "degraded"
        
        return health_data
    
    def get_scraping_statistics(self, days: int = 7) -> Dict:
        """
        Get scraping statistics for the specified period.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary with scraping statistics
        """
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        logs = self.session.query(ScrapingLog).filter(
            ScrapingLog.started_at >= cutoff_date
        ).all()
        
        if not logs:
            return {
                "period_days": days,
                "total_runs": 0,
                "success_rate": 0,
                "average_duration": 0,
                "total_products_scraped": 0,
                "total_errors": 0,
                "supermarket_performance": {}
            }
        
        successful_runs = [log for log in logs if log.status == "completed"]
        failed_runs = [log for log in logs if log.status == "failed"]
        
        # Calculate supermarket performance
        supermarket_performance = {}
        supermarkets = self.session.query(Supermarket).all()
        
        for supermarket in supermarkets:
            sm_logs = [log for log in logs if log.supermarket_id == supermarket.id]
            sm_successful = [log for log in sm_logs if log.status == "completed"]
            
            supermarket_performance[supermarket.name] = {
                "total_runs": len(sm_logs),
                "successful_runs": len(sm_successful),
                "success_rate": len(sm_successful) / len(sm_logs) * 100 if sm_logs else 0,
                "avg_duration": (
                    sum(log.duration_seconds for log in sm_successful if log.duration_seconds) /
                    len(sm_successful)
                ) if sm_successful else 0,
                "total_products": sum(log.products_found for log in sm_logs),
                "total_errors": sum(log.errors_count for log in sm_logs)
            }
        
        return {
            "period_days": days,
            "total_runs": len(logs),
            "successful_runs": len(successful_runs),
            "failed_runs": len(failed_runs),
            "success_rate": len(successful_runs) / len(logs) * 100,
            "average_duration": (
                sum(log.duration_seconds for log in successful_runs if log.duration_seconds) /
                len(successful_runs)
            ) if successful_runs else 0,
            "total_products_scraped": sum(log.products_found for log in logs),
            "total_errors": sum(log.errors_count for log in logs),
            "supermarket_performance": supermarket_performance
        }
    
    def get_data_quality_metrics(self) -> Dict:
        """
        Get data quality metrics.
        
        Returns:
            Dictionary with data quality information
        """
        # Product data quality
        total_products = self.session.query(Product).count()
        products_with_prices = self.session.query(Product).join(Price).distinct().count()
        products_with_images = self.session.query(Product).filter(
            Product.image_url.isnot(None)
        ).count()
        products_with_descriptions = self.session.query(Product).filter(
            Product.description.isnot(None)
        ).count()
        
        # Price data quality
        total_prices = self.session.query(Price).count()
        current_prices = self.session.query(Price).filter_by(is_current=True).count()
        
        # Recent data freshness
        recent_cutoff = datetime.now(timezone.utc) - timedelta(hours=24)
        recent_products = self.session.query(Product).filter(
            Product.last_seen_at >= recent_cutoff
        ).count()
        
        return {
            "product_data_quality": {
                "total_products": total_products,
                "products_with_prices": products_with_prices,
                "products_with_images": products_with_images,
                "products_with_descriptions": products_with_descriptions,
                "price_coverage": (products_with_prices / total_products * 100) if total_products else 0,
                "image_coverage": (products_with_images / total_products * 100) if total_products else 0,
                "description_coverage": (products_with_descriptions / total_products * 100) if total_products else 0
            },
            "price_data_quality": {
                "total_price_records": total_prices,
                "current_price_records": current_prices,
                "price_freshness": (current_prices / total_prices * 100) if total_prices else 0
            },
            "data_freshness": {
                "products_updated_24h": recent_products,
                "freshness_percentage": (recent_products / total_products * 100) if total_products else 0
            }
        }
    
    def get_performance_alerts(self) -> List[Dict]:
        """
        Get performance alerts and warnings.
        
        Returns:
            List of alert dictionaries
        """
        alerts = []
        
        # Check for failed scraping runs in last 24 hours
        recent_cutoff = datetime.now(timezone.utc) - timedelta(hours=24)
        failed_runs = self.session.query(ScrapingLog).filter(
            and_(
                ScrapingLog.started_at >= recent_cutoff,
                ScrapingLog.status == "failed"
            )
        ).count()
        
        if failed_runs > 5:
            alerts.append({
                "type": "error",
                "component": "scraping",
                "message": f"{failed_runs} scraping runs failed in the last 24 hours",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        
        # Check for stale data
        stale_cutoff = datetime.now(timezone.utc) - timedelta(days=3)
        stale_supermarkets = self.session.query(Supermarket).filter(
            or_(
                Supermarket.last_scraped_at < stale_cutoff,
                Supermarket.last_scraped_at.is_(None)
            )
        ).count()
        
        if stale_supermarkets > 0:
            alerts.append({
                "type": "warning",
                "component": "data_freshness",
                "message": f"{stale_supermarkets} supermarkets have not been scraped in 3+ days",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        
        # Check disk space
        disk_usage = psutil.disk_usage('/')
        disk_percent = (disk_usage.used / disk_usage.total) * 100
        
        if disk_percent > 90:
            alerts.append({
                "type": "critical",
                "component": "storage",
                "message": f"Disk usage is at {disk_percent:.1f}%",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        elif disk_percent > 80:
            alerts.append({
                "type": "warning",
                "component": "storage",
                "message": f"Disk usage is at {disk_percent:.1f}%",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        
        # Check memory usage
        memory = psutil.virtual_memory()
        if memory.percent > 90:
            alerts.append({
                "type": "critical",
                "component": "memory",
                "message": f"Memory usage is at {memory.percent:.1f}%",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        elif memory.percent > 80:
            alerts.append({
                "type": "warning",
                "component": "memory",
                "message": f"Memory usage is at {memory.percent:.1f}%",
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        
        return alerts
    
    def _check_database_health(self) -> Dict:
        """Check database health."""
        try:
            # Test basic connectivity
            self.session.execute("SELECT 1")
            
            # Check if main tables exist and have data
            table_counts = {
                "supermarkets": self.session.query(Supermarket).count(),
                "products": self.session.query(Product).count(),
                "prices": self.session.query(Price).count()
            }
            
            if all(count >= 0 for count in table_counts.values()):
                return {
                    "status": "healthy",
                    "message": "Database is accessible and contains data",
                    "details": table_counts
                }
            else:
                return {
                    "status": "warning",
                    "message": "Database is accessible but may be missing data",
                    "details": table_counts
                }
                
        except Exception as e:
            return {
                "status": "critical",
                "message": f"Database connection failed: {str(e)}",
                "details": {}
            }
    
    def _check_celery_health(self) -> Dict:
        """Check Celery health."""
        try:
            # Check if Celery is responsive
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            
            if stats:
                active_workers = len(stats)
                return {
                    "status": "healthy",
                    "message": f"{active_workers} Celery workers are active",
                    "details": {"active_workers": active_workers}
                }
            else:
                return {
                    "status": "critical",
                    "message": "No Celery workers are responding",
                    "details": {"active_workers": 0}
                }
                
        except Exception as e:
            return {
                "status": "critical",
                "message": f"Celery health check failed: {str(e)}",
                "details": {}
            }
    
    def _check_scraping_health(self) -> Dict:
        """Check scraping system health."""
        try:
            # Check recent scraping activity
            recent_cutoff = datetime.now(timezone.utc) - timedelta(hours=12)
            recent_runs = self.session.query(ScrapingLog).filter(
                ScrapingLog.started_at >= recent_cutoff
            ).count()
            
            if recent_runs > 0:
                return {
                    "status": "healthy",
                    "message": f"{recent_runs} scraping runs in last 12 hours",
                    "details": {"recent_runs": recent_runs}
                }
            else:
                return {
                    "status": "warning",
                    "message": "No scraping activity in last 12 hours",
                    "details": {"recent_runs": 0}
                }
                
        except Exception as e:
            return {
                "status": "critical",
                "message": f"Scraping health check failed: {str(e)}",
                "details": {}
            }
    
    def _check_storage_health(self) -> Dict:
        """Check storage health."""
        try:
            disk_usage = psutil.disk_usage('/')
            disk_percent = (disk_usage.used / disk_usage.total) * 100
            
            if disk_percent < 80:
                status = "healthy"
            elif disk_percent < 90:
                status = "warning"
            else:
                status = "critical"
            
            return {
                "status": status,
                "message": f"Disk usage: {disk_percent:.1f}%",
                "details": {
                    "used_gb": disk_usage.used / (1024**3),
                    "total_gb": disk_usage.total / (1024**3),
                    "free_gb": disk_usage.free / (1024**3),
                    "percent_used": disk_percent
                }
            }
            
        except Exception as e:
            return {
                "status": "critical",
                "message": f"Storage health check failed: {str(e)}",
                "details": {}
            }
    
    def _check_memory_usage(self) -> Dict:
        """Check memory usage."""
        try:
            memory = psutil.virtual_memory()
            
            if memory.percent < 80:
                status = "healthy"
            elif memory.percent < 90:
                status = "warning"
            else:
                status = "critical"
            
            return {
                "status": status,
                "message": f"Memory usage: {memory.percent:.1f}%",
                "details": {
                    "used_gb": memory.used / (1024**3),
                    "total_gb": memory.total / (1024**3),
                    "available_gb": memory.available / (1024**3),
                    "percent_used": memory.percent
                }
            }
            
        except Exception as e:
            return {
                "status": "critical",
                "message": f"Memory health check failed: {str(e)}",
                "details": {}
            }
    
    def _get_performance_metrics(self) -> Dict:
        """Get performance metrics."""
        try:
            # Database metrics
            recent_cutoff = datetime.now(timezone.utc) - timedelta(hours=24)
            recent_logs = self.session.query(ScrapingLog).filter(
                ScrapingLog.started_at >= recent_cutoff
            ).all()
            
            avg_scraping_duration = 0
            if recent_logs:
                durations = [log.duration_seconds for log in recent_logs if log.duration_seconds]
                if durations:
                    avg_scraping_duration = sum(durations) / len(durations)
            
            return {
                "avg_scraping_duration_seconds": avg_scraping_duration,
                "scraping_runs_24h": len(recent_logs),
                "cpu_usage_percent": psutil.cpu_percent(interval=1),
                "memory_usage_percent": psutil.virtual_memory().percent
            }
            
        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {}
