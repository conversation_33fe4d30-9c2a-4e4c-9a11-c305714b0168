"""
Celery tasks for scraping operations.
"""
import logging
import subprocess
import os
from datetime import datetime, timezone
from typing import List, Dict, Optional

from celery import current_task
from sqlalchemy.orm import sessionmaker

from app.celery_app import celery_app
from app.db.database import engine
from app.db.models import Supermarket, ScrapingLog
from app.core.config import settings

logger = logging.getLogger(__name__)
Session = sessionmaker(bind=engine)


@celery_app.task(bind=True, max_retries=3)
def run_spider(self, spider_name: str, supermarket_id: int, **kwargs):
    """
    Run a specific spider for scraping.
    
    Args:
        spider_name: Name of the spider to run
        supermarket_id: ID of the supermarket being scraped
        **kwargs: Additional spider arguments
    """
    session = Session()
    log_entry = None
    
    try:
        # Create scraping log entry
        log_entry = ScrapingLog(
            supermarket_id=supermarket_id,
            spider_name=spider_name,
            status="started",
            started_at=datetime.now(timezone.utc)
        )
        session.add(log_entry)
        session.commit()
        
        # Update task state
        self.update_state(
            state="PROGRESS",
            meta={"status": "Starting spider", "spider": spider_name}
        )
        
        # Build scrapy command
        scrapy_project_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
            "scraper"
        )
        
        cmd = [
            "scrapy", "crawl", spider_name,
            "-s", f"JOBDIR=jobs/{spider_name}-{current_task.request.id}",
            "-L", "INFO"
        ]
        
        # Add custom settings
        for key, value in kwargs.items():
            cmd.extend(["-s", f"{key}={value}"])
        
        # Run the spider
        logger.info(f"Starting spider {spider_name} for supermarket {supermarket_id}")
        
        start_time = datetime.now(timezone.utc)
        
        result = subprocess.run(
            cmd,
            cwd=scrapy_project_path,
            capture_output=True,
            text=True,
            timeout=3600  # 1 hour timeout
        )
        
        end_time = datetime.now(timezone.utc)
        duration = (end_time - start_time).total_seconds()
        
        # Parse scrapy output for statistics
        stats = parse_scrapy_output(result.stdout, result.stderr)
        
        # Update log entry
        log_entry.status = "completed" if result.returncode == 0 else "failed"
        log_entry.completed_at = end_time
        log_entry.duration_seconds = duration
        log_entry.products_found = stats.get("item_scraped_count", 0)
        log_entry.products_updated = stats.get("item_scraped_count", 0)  # Simplified
        log_entry.errors_count = stats.get("spider_exceptions", 0)
        
        if result.returncode != 0:
            log_entry.error_message = result.stderr[:1000]  # Limit error message length
            log_entry.error_details = {
                "return_code": result.returncode,
                "stdout": result.stdout[-1000:],  # Last 1000 chars
                "stderr": result.stderr[-1000:]
            }
        
        session.commit()
        
        # Update supermarket last_scraped_at
        supermarket = session.query(Supermarket).filter_by(id=supermarket_id).first()
        if supermarket:
            supermarket.last_scraped_at = end_time
            session.commit()
        
        logger.info(f"Spider {spider_name} completed with return code {result.returncode}")
        
        return {
            "status": "completed" if result.returncode == 0 else "failed",
            "spider_name": spider_name,
            "supermarket_id": supermarket_id,
            "duration": duration,
            "stats": stats,
            "return_code": result.returncode
        }
        
    except subprocess.TimeoutExpired:
        logger.error(f"Spider {spider_name} timed out")
        if log_entry:
            log_entry.status = "failed"
            log_entry.error_message = "Spider execution timed out"
            log_entry.completed_at = datetime.now(timezone.utc)
            session.commit()
        
        self.retry(countdown=300, exc=Exception("Spider timed out"))
        
    except Exception as exc:
        logger.error(f"Error running spider {spider_name}: {exc}")
        
        if log_entry:
            log_entry.status = "failed"
            log_entry.error_message = str(exc)[:1000]
            log_entry.completed_at = datetime.now(timezone.utc)
            session.commit()
        
        # Retry with exponential backoff
        self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        
    finally:
        session.close()


@celery_app.task
def run_scheduled_scraping():
    """
    Run scheduled scraping for all active supermarkets.
    """
    session = Session()
    
    try:
        # Get all active supermarkets
        active_supermarkets = session.query(Supermarket).filter_by(is_active=True).all()
        
        if not active_supermarkets:
            logger.info("No active supermarkets found for scheduled scraping")
            return {"message": "No active supermarkets", "count": 0}
        
        # Start scraping tasks for each supermarket
        task_ids = []
        for supermarket in active_supermarkets:
            task = run_spider.delay(
                spider_name=supermarket.spider_name,
                supermarket_id=supermarket.id
            )
            task_ids.append({
                "supermarket": supermarket.name,
                "spider": supermarket.spider_name,
                "task_id": task.id
            })
        
        logger.info(f"Started scheduled scraping for {len(active_supermarkets)} supermarkets")
        
        return {
            "message": f"Started scraping for {len(active_supermarkets)} supermarkets",
            "tasks": task_ids
        }
        
    except Exception as exc:
        logger.error(f"Error in scheduled scraping: {exc}")
        raise
        
    finally:
        session.close()


@celery_app.task
def run_supermarket_scraping(supermarket_id: int):
    """
    Run scraping for a specific supermarket.
    
    Args:
        supermarket_id: ID of the supermarket to scrape
    """
    session = Session()
    
    try:
        supermarket = session.query(Supermarket).filter_by(id=supermarket_id).first()
        
        if not supermarket:
            raise ValueError(f"Supermarket with ID {supermarket_id} not found")
        
        if not supermarket.is_active:
            raise ValueError(f"Supermarket {supermarket.name} is not active")
        
        # Start scraping task
        task = run_spider.delay(
            spider_name=supermarket.spider_name,
            supermarket_id=supermarket.id
        )
        
        logger.info(f"Started scraping for {supermarket.name} (task: {task.id})")
        
        return {
            "message": f"Started scraping for {supermarket.name}",
            "supermarket_id": supermarket_id,
            "task_id": task.id
        }
        
    except Exception as exc:
        logger.error(f"Error starting scraping for supermarket {supermarket_id}: {exc}")
        raise
        
    finally:
        session.close()


def parse_scrapy_output(stdout: str, stderr: str) -> Dict:
    """
    Parse Scrapy output to extract statistics.
    
    Args:
        stdout: Standard output from Scrapy
        stderr: Standard error from Scrapy
        
    Returns:
        Dictionary with parsed statistics
    """
    stats = {}
    
    # Common Scrapy statistics patterns
    patterns = {
        "item_scraped_count": r"'item_scraped_count': (\d+)",
        "response_received_count": r"'response_received_count': (\d+)",
        "spider_exceptions": r"'spider_exceptions/\w+': (\d+)",
        "downloader_request_count": r"'downloader/request_count': (\d+)",
        "downloader_response_count": r"'downloader/response_count': (\d+)",
    }
    
    import re
    
    # Search in both stdout and stderr
    output = stdout + stderr
    
    for stat_name, pattern in patterns.items():
        match = re.search(pattern, output)
        if match:
            stats[stat_name] = int(match.group(1))
        else:
            stats[stat_name] = 0
    
    return stats


@celery_app.task
def test_spider_connectivity(spider_name: str):
    """
    Test if a spider can connect to its target website.
    
    Args:
        spider_name: Name of the spider to test
    """
    try:
        scrapy_project_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 
            "scraper"
        )
        
        # Run a dry-run test
        cmd = [
            "scrapy", "crawl", spider_name,
            "-s", "CLOSESPIDER_PAGECOUNT=1",  # Stop after 1 page
            "-s", "ROBOTSTXT_OBEY=False",     # Ignore robots.txt for testing
            "-L", "ERROR"                     # Only show errors
        ]
        
        result = subprocess.run(
            cmd,
            cwd=scrapy_project_path,
            capture_output=True,
            text=True,
            timeout=60  # 1 minute timeout for test
        )
        
        return {
            "spider_name": spider_name,
            "success": result.returncode == 0,
            "return_code": result.returncode,
            "output": result.stdout[-500:] if result.stdout else "",
            "error": result.stderr[-500:] if result.stderr else ""
        }
        
    except subprocess.TimeoutExpired:
        return {
            "spider_name": spider_name,
            "success": False,
            "error": "Test timed out"
        }
    except Exception as exc:
        return {
            "spider_name": spider_name,
            "success": False,
            "error": str(exc)
        }
