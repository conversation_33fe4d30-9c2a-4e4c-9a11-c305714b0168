"""
Celery tasks for maintenance and monitoring operations.
"""
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List
from sqlalchemy.orm import sessionmaker
from sqlalchemy import func, and_

from app.celery_app import celery_app
from app.db.database import engine
from app.db.models import (
    Supermarket, Product, Price, Discount, ScrapingLog, Category
)
from app.core.config import settings

logger = logging.getLogger(__name__)
Session = sessionmaker(bind=engine)


@celery_app.task
def cleanup_old_data():
    """
    Clean up old data based on retention policies.
    """
    session = Session()
    
    try:
        cleanup_stats = {
            "old_prices_deleted": 0,
            "expired_discounts_deleted": 0,
            "old_logs_deleted": 0,
            "unavailable_products_deleted": 0
        }
        
        # Clean up old price records (keep only last 30 days)
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
        
        old_prices = session.query(Price).filter(
            and_(
                Price.scraped_at < cutoff_date,
                Price.is_current == False
            )
        )
        cleanup_stats["old_prices_deleted"] = old_prices.count()
        old_prices.delete()
        
        # Clean up expired discounts
        expired_discounts = session.query(Discount).filter(
            and_(
                Discount.valid_until < datetime.now(timezone.utc),
                Discount.is_active == True
            )
        )
        expired_discounts.update({"is_active": False})
        cleanup_stats["expired_discounts_deleted"] = expired_discounts.count()
        
        # Clean up old scraping logs (keep only last 90 days)
        log_cutoff_date = datetime.now(timezone.utc) - timedelta(days=90)
        old_logs = session.query(ScrapingLog).filter(
            ScrapingLog.started_at < log_cutoff_date
        )
        cleanup_stats["old_logs_deleted"] = old_logs.count()
        old_logs.delete()
        
        # Mark products as unavailable if not seen in 7 days
        product_cutoff_date = datetime.now(timezone.utc) - timedelta(days=7)
        unavailable_products = session.query(Product).filter(
            and_(
                Product.last_seen_at < product_cutoff_date,
                Product.is_available == True
            )
        )
        unavailable_products.update({"is_available": False})
        cleanup_stats["unavailable_products_deleted"] = unavailable_products.count()
        
        session.commit()
        
        logger.info(f"Cleanup completed: {cleanup_stats}")
        
        return cleanup_stats
        
    except Exception as exc:
        logger.error(f"Error during cleanup: {exc}")
        session.rollback()
        raise
        
    finally:
        session.close()


@celery_app.task
def update_price_trends():
    """
    Update price trend analysis for products.
    """
    session = Session()
    
    try:
        # Get products with price history
        products_with_prices = session.query(Product).join(Price).group_by(
            Product.id
        ).having(func.count(Price.id) >= 3).all()  # At least 3 price points
        
        trends_updated = 0
        
        for product in products_with_prices:
            # Get recent price history (last 30 days)
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
            
            prices = session.query(Price).filter(
                and_(
                    Price.product_id == product.id,
                    Price.scraped_at >= cutoff_date
                )
            ).order_by(Price.scraped_at).all()
            
            if len(prices) >= 3:
                trend = calculate_price_trend([p.current_price for p in prices])
                
                # Store trend in product metadata (you might want to add a trends table)
                # For now, we'll just log it
                logger.debug(f"Product {product.name}: trend = {trend}")
                trends_updated += 1
        
        logger.info(f"Updated price trends for {trends_updated} products")
        
        return {
            "trends_updated": trends_updated,
            "total_products_analyzed": len(products_with_prices)
        }
        
    except Exception as exc:
        logger.error(f"Error updating price trends: {exc}")
        raise
        
    finally:
        session.close()


@celery_app.task
def health_check():
    """
    Perform system health checks.
    """
    session = Session()
    
    try:
        health_status = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "database_connection": False,
            "active_supermarkets": 0,
            "recent_scraping_activity": False,
            "total_products": 0,
            "active_discounts": 0,
            "system_status": "unknown"
        }
        
        # Test database connection
        try:
            session.execute("SELECT 1")
            health_status["database_connection"] = True
        except Exception:
            health_status["database_connection"] = False
        
        # Count active supermarkets
        health_status["active_supermarkets"] = session.query(Supermarket).filter_by(
            is_active=True
        ).count()
        
        # Check recent scraping activity (last 24 hours)
        recent_cutoff = datetime.now(timezone.utc) - timedelta(hours=24)
        recent_logs = session.query(ScrapingLog).filter(
            ScrapingLog.started_at >= recent_cutoff
        ).count()
        health_status["recent_scraping_activity"] = recent_logs > 0
        
        # Count total products
        health_status["total_products"] = session.query(Product).count()
        
        # Count active discounts
        health_status["active_discounts"] = session.query(Discount).filter_by(
            is_active=True
        ).count()
        
        # Determine overall system status
        if (health_status["database_connection"] and 
            health_status["active_supermarkets"] > 0 and
            health_status["total_products"] > 0):
            health_status["system_status"] = "healthy"
        elif health_status["database_connection"]:
            health_status["system_status"] = "degraded"
        else:
            health_status["system_status"] = "unhealthy"
        
        logger.info(f"Health check completed: {health_status['system_status']}")
        
        return health_status
        
    except Exception as exc:
        logger.error(f"Error during health check: {exc}")
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "system_status": "error",
            "error": str(exc)
        }
        
    finally:
        session.close()


@celery_app.task
def generate_daily_report():
    """
    Generate daily summary report.
    """
    session = Session()
    
    try:
        # Calculate date range for yesterday
        today = datetime.now(timezone.utc).date()
        yesterday = today - timedelta(days=1)
        yesterday_start = datetime.combine(yesterday, datetime.min.time()).replace(tzinfo=timezone.utc)
        yesterday_end = datetime.combine(yesterday, datetime.max.time()).replace(tzinfo=timezone.utc)
        
        report = {
            "date": yesterday.isoformat(),
            "scraping_summary": {},
            "product_summary": {},
            "discount_summary": {},
            "performance_metrics": {}
        }
        
        # Scraping summary
        scraping_logs = session.query(ScrapingLog).filter(
            and_(
                ScrapingLog.started_at >= yesterday_start,
                ScrapingLog.started_at <= yesterday_end
            )
        ).all()
        
        report["scraping_summary"] = {
            "total_runs": len(scraping_logs),
            "successful_runs": len([log for log in scraping_logs if log.status == "completed"]),
            "failed_runs": len([log for log in scraping_logs if log.status == "failed"]),
            "total_products_scraped": sum(log.products_found for log in scraping_logs),
            "total_errors": sum(log.errors_count for log in scraping_logs),
            "average_duration": (
                sum(log.duration_seconds for log in scraping_logs if log.duration_seconds) / 
                len([log for log in scraping_logs if log.duration_seconds])
            ) if scraping_logs else 0
        }
        
        # Product summary
        total_products = session.query(Product).count()
        available_products = session.query(Product).filter_by(is_available=True).count()
        
        report["product_summary"] = {
            "total_products": total_products,
            "available_products": available_products,
            "unavailable_products": total_products - available_products,
            "products_by_supermarket": {}
        }
        
        # Products by supermarket
        supermarkets = session.query(Supermarket).all()
        for supermarket in supermarkets:
            product_count = session.query(Product).filter_by(
                supermarket_id=supermarket.id,
                is_available=True
            ).count()
            report["product_summary"]["products_by_supermarket"][supermarket.name] = product_count
        
        # Discount summary
        active_discounts = session.query(Discount).filter_by(is_active=True).count()
        
        # New discounts added yesterday
        new_discounts = session.query(Discount).filter(
            and_(
                Discount.created_at >= yesterday_start,
                Discount.created_at <= yesterday_end
            )
        ).count()
        
        report["discount_summary"] = {
            "active_discounts": active_discounts,
            "new_discounts_yesterday": new_discounts
        }
        
        # Performance metrics
        report["performance_metrics"] = {
            "database_size_mb": get_database_size_mb(session),
            "avg_response_time": calculate_avg_response_time(scraping_logs)
        }
        
        logger.info(f"Daily report generated for {yesterday}")
        
        return report
        
    except Exception as exc:
        logger.error(f"Error generating daily report: {exc}")
        raise
        
    finally:
        session.close()


@celery_app.task
def optimize_database():
    """
    Perform database optimization tasks.
    """
    session = Session()
    
    try:
        optimization_stats = {
            "tables_analyzed": 0,
            "indexes_rebuilt": 0,
            "vacuum_performed": False
        }
        
        # For SQLite, run VACUUM to optimize database
        if "sqlite" in settings.database_url:
            session.execute("VACUUM")
            optimization_stats["vacuum_performed"] = True
        
        # For PostgreSQL, run ANALYZE on main tables
        elif "postgresql" in settings.database_url:
            tables = ["products", "prices", "discounts", "supermarkets", "scraping_logs"]
            for table in tables:
                session.execute(f"ANALYZE {table}")
                optimization_stats["tables_analyzed"] += 1
        
        session.commit()
        
        logger.info(f"Database optimization completed: {optimization_stats}")
        
        return optimization_stats
        
    except Exception as exc:
        logger.error(f"Error during database optimization: {exc}")
        raise
        
    finally:
        session.close()


def calculate_price_trend(prices: List[float]) -> str:
    """Calculate price trend from a list of prices."""
    if len(prices) < 2:
        return "stable"
    
    # Simple trend calculation
    recent_avg = sum(prices[-3:]) / len(prices[-3:])
    older_avg = sum(prices[:3]) / len(prices[:3])
    
    change_percentage = ((recent_avg - older_avg) / older_avg) * 100
    
    if change_percentage > 5:
        return "increasing"
    elif change_percentage < -5:
        return "decreasing"
    else:
        return "stable"


def get_database_size_mb(session) -> float:
    """Get database size in MB."""
    try:
        if "sqlite" in settings.database_url:
            import os
            db_path = settings.database_url.replace("sqlite:///", "")
            if os.path.exists(db_path):
                return os.path.getsize(db_path) / (1024 * 1024)
        elif "postgresql" in settings.database_url:
            result = session.execute(
                "SELECT pg_size_pretty(pg_database_size(current_database()))"
            ).scalar()
            # Parse the result to extract MB value
            if "MB" in result:
                return float(result.replace(" MB", ""))
            elif "GB" in result:
                return float(result.replace(" GB", "")) * 1024
    except Exception:
        pass
    
    return 0.0


def calculate_avg_response_time(scraping_logs: List) -> float:
    """Calculate average response time from scraping logs."""
    if not scraping_logs:
        return 0.0
    
    durations = [log.duration_seconds for log in scraping_logs if log.duration_seconds]
    if not durations:
        return 0.0
    
    return sum(durations) / len(durations)
