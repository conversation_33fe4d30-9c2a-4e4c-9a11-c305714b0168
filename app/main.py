"""
FastAPI main application for supermarket scraper.
"""
from fastapi import FastAPI, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.database import get_db, create_tables
from app.api import supermarkets, products, scraping

# Create database tables
create_tables()

# Initialize FastAPI app
app = FastAPI(
    title="Supermarket Price Comparison Scraper",
    description="A comprehensive web scraping solution for comparing supermarket prices",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=settings.allowed_methods,
    allow_headers=settings.allowed_headers,
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Include API routers
app.include_router(supermarkets.router, prefix="/api/v1/supermarkets", tags=["supermarkets"])
app.include_router(products.router, prefix="/api/v1/products", tags=["products"])
app.include_router(scraping.router, prefix="/api/v1/scraping", tags=["scraping"])


@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main application page."""
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Supermarket Price Comparison</title>
        <link href="/static/css/style.css" rel="stylesheet">
    </head>
    <body>
        <header class="header">
            <div class="container">
                <h1>Supermarket Price Comparison</h1>
            </div>
        </header>

        <main id="app">
            <div class="container">
                <div class="card text-center">
                    <div class="spinner"></div>
                    <p class="mt-4">Loading application...</p>
                </div>
            </div>
        </main>

        <script src="/static/js/app.js"></script>
    </body>
    </html>
    """


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "message": "Supermarket scraper API is running"}


@app.get("/favicon.ico")
async def favicon():
    """Serve favicon to prevent 404 errors."""
    from fastapi.responses import FileResponse
    import os

    favicon_path = os.path.join("static", "favicon.ico")
    if os.path.exists(favicon_path):
        return FileResponse(favicon_path)
    else:
        # Return a simple response to prevent 404
        return {"message": "favicon not found"}


@app.on_event("startup")
async def startup_event():
    """Application startup event."""
    print(f"Starting Supermarket Scraper API on {settings.api_host}:{settings.api_port}")
    print(f"Documentation available at: http://{settings.api_host}:{settings.api_port}/docs")


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event."""
    print("Shutting down Supermarket Scraper API")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_debug
    )
