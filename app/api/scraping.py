"""
API endpoints for scraping management and monitoring.
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import desc
from pydantic import BaseModel, ConfigDict

from app.db.database import get_db
from app.db.models import ScrapingLog, Supermarket
from app.services.scraping_service import ScrapingService

router = APIRouter()


# Pydantic models
class ScrapingLogResponse(BaseModel):
    id: int
    supermarket_name: str
    spider_name: str
    status: str
    products_found: int
    products_updated: int
    products_created: int
    errors_count: int
    started_at: datetime
    completed_at: Optional[datetime]
    duration_seconds: Optional[float]
    error_message: Optional[str]

    model_config = ConfigDict(from_attributes=True)


class ScrapingStatsResponse(BaseModel):
    total_runs: int
    successful_runs: int
    failed_runs: int
    total_products_scraped: int
    total_errors: int
    average_duration: Optional[float]
    last_run: Optional[datetime]


@router.get("/logs", response_model=List[ScrapingLogResponse])
async def get_scraping_logs(
    skip: int = 0,
    limit: int = 100,
    supermarket_id: Optional[int] = None,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Get scraping logs with filtering options."""
    query = db.query(ScrapingLog).join(Supermarket)
    
    if supermarket_id:
        query = query.filter(ScrapingLog.supermarket_id == supermarket_id)
    
    if status:
        query = query.filter(ScrapingLog.status == status)
    
    logs = query.order_by(desc(ScrapingLog.started_at)).offset(skip).limit(limit).all()
    
    result = []
    for log in logs:
        result.append(ScrapingLogResponse(
            id=log.id,
            supermarket_name=log.supermarket.name,
            spider_name=log.spider_name,
            status=log.status,
            products_found=log.products_found,
            products_updated=log.products_updated,
            products_created=log.products_created,
            errors_count=log.errors_count,
            started_at=log.started_at,
            completed_at=log.completed_at,
            duration_seconds=log.duration_seconds,
            error_message=log.error_message
        ))
    
    return result


@router.get("/stats", response_model=ScrapingStatsResponse)
async def get_scraping_stats(
    supermarket_id: Optional[int] = None,
    db: Session = Depends(get_db)
):
    """Get overall scraping statistics."""
    query = db.query(ScrapingLog)
    
    if supermarket_id:
        query = query.filter(ScrapingLog.supermarket_id == supermarket_id)
    
    logs = query.all()
    
    if not logs:
        return ScrapingStatsResponse(
            total_runs=0,
            successful_runs=0,
            failed_runs=0,
            total_products_scraped=0,
            total_errors=0,
            average_duration=None,
            last_run=None
        )
    
    successful_runs = len([log for log in logs if log.status == 'completed'])
    failed_runs = len([log for log in logs if log.status == 'failed'])
    total_products_scraped = sum(log.products_found for log in logs)
    total_errors = sum(log.errors_count for log in logs)
    
    # Calculate average duration for completed runs
    completed_logs = [log for log in logs if log.duration_seconds is not None]
    average_duration = None
    if completed_logs:
        average_duration = sum(log.duration_seconds for log in completed_logs) / len(completed_logs)
    
    # Get last run
    last_log = max(logs, key=lambda x: x.started_at)
    
    return ScrapingStatsResponse(
        total_runs=len(logs),
        successful_runs=successful_runs,
        failed_runs=failed_runs,
        total_products_scraped=total_products_scraped,
        total_errors=total_errors,
        average_duration=average_duration,
        last_run=last_log.started_at
    )


@router.post("/start")
async def start_scraping(
    supermarket_id: Optional[int] = None,
    spider_name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Start scraping for specific supermarket or all active supermarkets."""
    scraping_service = ScrapingService()
    
    if supermarket_id:
        # Start scraping for specific supermarket
        supermarket = db.query(Supermarket).filter(Supermarket.id == supermarket_id).first()
        if not supermarket:
            raise HTTPException(status_code=404, detail="Supermarket not found")
        
        if not supermarket.is_active:
            raise HTTPException(status_code=400, detail="Cannot scrape inactive supermarket")
        
        task_id = scraping_service.start_scraping(supermarket.spider_name)
        return {
            "message": f"Scraping started for {supermarket.name}",
            "task_id": task_id,
            "spider_name": supermarket.spider_name
        }
    
    elif spider_name:
        # Start scraping for specific spider
        supermarket = db.query(Supermarket).filter(Supermarket.spider_name == spider_name).first()
        if not supermarket:
            raise HTTPException(status_code=404, detail="Spider not found")
        
        if not supermarket.is_active:
            raise HTTPException(status_code=400, detail="Cannot scrape inactive supermarket")
        
        task_id = scraping_service.start_scraping(spider_name)
        return {
            "message": f"Scraping started for spider {spider_name}",
            "task_id": task_id,
            "spider_name": spider_name
        }
    
    else:
        # Start scraping for all active supermarkets
        active_supermarkets = db.query(Supermarket).filter(Supermarket.is_active == True).all()
        
        if not active_supermarkets:
            raise HTTPException(status_code=400, detail="No active supermarkets found")
        
        task_ids = []
        for supermarket in active_supermarkets:
            task_id = scraping_service.start_scraping(supermarket.spider_name)
            task_ids.append({
                "supermarket": supermarket.name,
                "spider_name": supermarket.spider_name,
                "task_id": task_id
            })
        
        return {
            "message": f"Scraping started for {len(active_supermarkets)} supermarkets",
            "tasks": task_ids
        }


@router.post("/stop")
async def stop_scraping(
    task_id: Optional[str] = None,
    spider_name: Optional[str] = None
):
    """Stop running scraping tasks."""
    scraping_service = ScrapingService()
    
    if task_id:
        # Stop specific task
        result = scraping_service.stop_scraping(task_id)
        return {"message": f"Task {task_id} stop requested", "result": result}
    
    elif spider_name:
        # Stop all tasks for specific spider
        result = scraping_service.stop_spider(spider_name)
        return {"message": f"All tasks for spider {spider_name} stop requested", "result": result}
    
    else:
        # Stop all running tasks
        result = scraping_service.stop_all_scraping()
        return {"message": "All scraping tasks stop requested", "result": result}


@router.get("/status")
async def get_scraping_status():
    """Get current scraping status."""
    scraping_service = ScrapingService()
    status = scraping_service.get_scraping_status()

    return {
        "active_tasks": status.get("active_tasks", 0),
        "pending_tasks": status.get("pending_tasks", 0),
        "running_spiders": status.get("running_spiders", []),
        "system_status": status.get("system_status", "unknown")
    }


@router.post("/test-celery")
async def test_celery():
    """Test Celery connectivity by running a simple task."""
    try:
        from app.tasks.scraping_tasks import run_spider

        # Try to start a test task
        task = run_spider.delay(
            spider_name="test_spider",
            supermarket_id=1
        )

        return {
            "message": "Celery test task started",
            "task_id": task.id,
            "status": "success"
        }
    except Exception as e:
        return {
            "message": f"Celery test failed: {str(e)}",
            "status": "error"
        }


@router.delete("/logs/{log_id}")
async def delete_scraping_log(log_id: int, db: Session = Depends(get_db)):
    """Delete a specific scraping log."""
    log = db.query(ScrapingLog).filter(ScrapingLog.id == log_id).first()
    if not log:
        raise HTTPException(status_code=404, detail="Scraping log not found")
    
    db.delete(log)
    db.commit()
    
    return {"message": "Scraping log deleted successfully"}


@router.delete("/logs")
async def cleanup_old_logs(
    days: int = 30,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """Clean up old scraping logs."""
    from datetime import datetime, timedelta, timezone
    
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
    
    query = db.query(ScrapingLog).filter(ScrapingLog.started_at < cutoff_date)
    
    if status:
        query = query.filter(ScrapingLog.status == status)
    
    deleted_count = query.count()
    query.delete()
    db.commit()
    
    return {
        "message": f"Deleted {deleted_count} scraping logs older than {days} days",
        "deleted_count": deleted_count
    }
