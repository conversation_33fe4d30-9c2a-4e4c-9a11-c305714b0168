"""
API endpoints for product management and price comparison.
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from pydantic import BaseModel, ConfigDict

from app.db.database import get_db
from app.db.models import Product, Price, Discount, Supermarket, Category

router = APIRouter()


# Pydantic models
class ProductResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    brand: Optional[str]
    sku: Optional[str]
    barcode: Optional[str]
    size: Optional[str]
    unit: Optional[str]
    product_url: Optional[str]
    image_url: Optional[str]
    is_available: bool
    supermarket_name: str
    category_name: Optional[str]
    current_price: Optional[float]
    regular_price: Optional[float]
    currency: Optional[str]
    last_seen_at: datetime

    model_config = ConfigDict(from_attributes=True)


class PriceComparisonResponse(BaseModel):
    product_name: str
    brand: Optional[str]
    size: Optional[str]
    prices: List[dict]  # List of {supermarket, current_price, regular_price, discount_info}
    best_price: dict
    price_range: dict


class DiscountResponse(BaseModel):
    id: int
    product_name: str
    supermarket_name: str
    discount_type: str
    discount_value: Optional[float]
    original_price: float
    discounted_price: float
    promotion_text: Optional[str]
    promotion_code: Optional[str]
    valid_from: Optional[datetime]
    valid_until: Optional[datetime]
    is_active: bool

    model_config = ConfigDict(from_attributes=True)


@router.get("/", response_model=List[ProductResponse])
async def list_products(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    supermarket_id: Optional[int] = None,
    category_id: Optional[int] = None,
    brand: Optional[str] = None,
    available_only: bool = True,
    db: Session = Depends(get_db)
):
    """List products with filtering options."""
    query = db.query(Product).join(Supermarket)
    
    # Apply filters
    if available_only:
        query = query.filter(Product.is_available == True)
    
    if supermarket_id:
        query = query.filter(Product.supermarket_id == supermarket_id)
    
    if category_id:
        query = query.filter(Product.category_id == category_id)
    
    if brand:
        query = query.filter(Product.brand.ilike(f"%{brand}%"))
    
    if search:
        search_filter = or_(
            Product.name.ilike(f"%{search}%"),
            Product.description.ilike(f"%{search}%"),
            Product.brand.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    products = query.offset(skip).limit(limit).all()
    
    # Format response with additional data
    result = []
    for product in products:
        # Get current price
        current_price_obj = db.query(Price).filter(
            and_(Price.product_id == product.id, Price.is_current == True)
        ).first()
        
        # Get category name
        category_name = None
        if product.category:
            category_name = product.category.name
        
        result.append(ProductResponse(
            id=product.id,
            name=product.name,
            description=product.description,
            brand=product.brand,
            sku=product.sku,
            barcode=product.barcode,
            size=product.size,
            unit=product.unit,
            product_url=product.product_url,
            image_url=product.image_url,
            is_available=product.is_available,
            supermarket_name=product.supermarket.name,
            category_name=category_name,
            current_price=current_price_obj.current_price if current_price_obj else None,
            regular_price=current_price_obj.regular_price if current_price_obj else None,
            currency=current_price_obj.currency if current_price_obj else None,
            last_seen_at=product.last_seen_at
        ))
    
    return result


@router.get("/compare")
async def compare_prices(
    search: str = Query(..., description="Product name to search for"),
    db: Session = Depends(get_db)
):
    """Compare prices for similar products across supermarkets."""
    # Find products with similar names
    products = db.query(Product).filter(
        and_(
            Product.name.ilike(f"%{search}%"),
            Product.is_available == True
        )
    ).all()
    
    if not products:
        raise HTTPException(status_code=404, detail="No products found matching search criteria")
    
    # Group products by similar characteristics
    product_groups = {}
    
    for product in products:
        # Create a key based on name similarity and brand
        key = f"{product.name.lower()}_{product.brand or 'unknown'}_{product.size or 'unknown'}"
        
        if key not in product_groups:
            product_groups[key] = []
        
        # Get current price and discount info
        current_price_obj = db.query(Price).filter(
            and_(Price.product_id == product.id, Price.is_current == True)
        ).first()
        
        active_discounts = db.query(Discount).filter(
            and_(
                Discount.product_id == product.id,
                Discount.is_active == True
            )
        ).all()
        
        product_info = {
            "product_id": product.id,
            "supermarket": product.supermarket.name,
            "current_price": current_price_obj.current_price if current_price_obj else None,
            "regular_price": current_price_obj.regular_price if current_price_obj else None,
            "currency": current_price_obj.currency if current_price_obj else "USD",
            "discounts": [
                {
                    "type": discount.discount_type,
                    "value": discount.discount_value,
                    "text": discount.promotion_text
                }
                for discount in active_discounts
            ],
            "product_url": product.product_url
        }
        
        product_groups[key].append(product_info)
    
    # Format comparison results
    comparisons = []
    for key, group in product_groups.items():
        if len(group) > 1:  # Only include products available in multiple supermarkets
            # Find best price
            valid_prices = [p for p in group if p["current_price"] is not None]
            if valid_prices:
                best_price = min(valid_prices, key=lambda x: x["current_price"])
                price_range = {
                    "min": min(p["current_price"] for p in valid_prices),
                    "max": max(p["current_price"] for p in valid_prices),
                    "currency": valid_prices[0]["currency"]
                }
                
                # Get product details from first item
                first_product = products[0]
                
                comparisons.append(PriceComparisonResponse(
                    product_name=first_product.name,
                    brand=first_product.brand,
                    size=first_product.size,
                    prices=group,
                    best_price=best_price,
                    price_range=price_range
                ))
    
    return comparisons


@router.get("/discounts", response_model=List[DiscountResponse])
async def list_discounts(
    skip: int = 0,
    limit: int = 100,
    supermarket_id: Optional[int] = None,
    active_only: bool = True,
    db: Session = Depends(get_db)
):
    """List current discounts and promotions."""
    query = db.query(Discount).join(Product).join(Supermarket)
    
    if active_only:
        query = query.filter(Discount.is_active == True)
    
    if supermarket_id:
        query = query.filter(Product.supermarket_id == supermarket_id)
    
    # Order by discount value (highest first)
    query = query.order_by(desc(Discount.discount_value))
    
    discounts = query.offset(skip).limit(limit).all()
    
    result = []
    for discount in discounts:
        result.append(DiscountResponse(
            id=discount.id,
            product_name=discount.product.name,
            supermarket_name=discount.product.supermarket.name,
            discount_type=discount.discount_type,
            discount_value=discount.discount_value,
            original_price=discount.original_price,
            discounted_price=discount.discounted_price,
            promotion_text=discount.promotion_text,
            promotion_code=discount.promotion_code,
            valid_from=discount.valid_from,
            valid_until=discount.valid_until,
            is_active=discount.is_active
        ))
    
    return result


@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(product_id: int, db: Session = Depends(get_db)):
    """Get a specific product by ID."""
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    
    # Get current price
    current_price_obj = db.query(Price).filter(
        and_(Price.product_id == product.id, Price.is_current == True)
    ).first()
    
    # Get category name
    category_name = None
    if product.category:
        category_name = product.category.name
    
    return ProductResponse(
        id=product.id,
        name=product.name,
        description=product.description,
        brand=product.brand,
        sku=product.sku,
        barcode=product.barcode,
        size=product.size,
        unit=product.unit,
        product_url=product.product_url,
        image_url=product.image_url,
        is_available=product.is_available,
        supermarket_name=product.supermarket.name,
        category_name=category_name,
        current_price=current_price_obj.current_price if current_price_obj else None,
        regular_price=current_price_obj.regular_price if current_price_obj else None,
        currency=current_price_obj.currency if current_price_obj else None,
        last_seen_at=product.last_seen_at
    )


@router.get("/{product_id}/price-history")
async def get_price_history(
    product_id: int,
    limit: int = 30,
    db: Session = Depends(get_db)
):
    """Get price history for a product."""
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    
    prices = db.query(Price).filter(
        Price.product_id == product_id
    ).order_by(desc(Price.scraped_at)).limit(limit).all()
    
    return {
        "product_id": product_id,
        "product_name": product.name,
        "supermarket": product.supermarket.name,
        "price_history": [
            {
                "date": price.scraped_at,
                "regular_price": price.regular_price,
                "current_price": price.current_price,
                "currency": price.currency,
                "is_current": price.is_current
            }
            for price in prices
        ]
    }
