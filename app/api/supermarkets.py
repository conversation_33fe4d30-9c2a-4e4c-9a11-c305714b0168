"""
API endpoints for supermarket management.
"""
import logging
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel, HttpUrl, ConfigDict

from app.db.database import get_db
from app.db.models import Supermarket, Product, Price, Discount
from app.services.scraping_service import ScrapingService

logger = logging.getLogger(__name__)

router = APIRouter()


# Pydantic models for API
class SupermarketCreate(BaseModel):
    name: str
    domain: str
    base_url: HttpUrl
    spider_name: str
    scraping_config: Optional[dict] = None
    is_active: bool = True


class SupermarketUpdate(BaseModel):
    name: Optional[str] = None
    domain: Optional[str] = None
    base_url: Optional[HttpUrl] = None
    spider_name: Optional[str] = None
    scraping_config: Optional[dict] = None
    is_active: Optional[bool] = None


class SupermarketResponse(BaseModel):
    id: int
    name: str
    domain: str
    base_url: str
    spider_name: str
    scraping_config: Optional[dict]
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime]
    last_scraped_at: Optional[datetime]

    model_config = ConfigDict(from_attributes=True)


class SupermarketStats(BaseModel):
    id: int
    name: str
    total_products: int
    active_products: int
    total_discounts: int
    active_discounts: int
    last_scraped_at: Optional[datetime]


@router.get("/", response_model=List[SupermarketResponse])
async def list_supermarkets(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False,
    db: Session = Depends(get_db)
):
    """List all supermarkets."""
    query = db.query(Supermarket)
    
    if active_only:
        query = query.filter(Supermarket.is_active == True)
    
    supermarkets = query.offset(skip).limit(limit).all()
    return supermarkets


@router.get("/stats", response_model=List[SupermarketStats])
async def get_supermarket_stats(db: Session = Depends(get_db)):
    """Get statistics for all supermarkets."""
    supermarkets = db.query(Supermarket).all()
    stats = []
    
    for supermarket in supermarkets:
        total_products = db.query(Product).filter(
            Product.supermarket_id == supermarket.id
        ).count()
        
        active_products = db.query(Product).filter(
            Product.supermarket_id == supermarket.id,
            Product.is_available == True
        ).count()
        
        total_discounts = db.query(Discount).join(Product).filter(
            Product.supermarket_id == supermarket.id
        ).count()
        
        active_discounts = db.query(Discount).join(Product).filter(
            Product.supermarket_id == supermarket.id,
            Discount.is_active == True
        ).count()
        
        stats.append(SupermarketStats(
            id=supermarket.id,
            name=supermarket.name,
            total_products=total_products,
            active_products=active_products,
            total_discounts=total_discounts,
            active_discounts=active_discounts,
            last_scraped_at=supermarket.last_scraped_at
        ))
    
    return stats


@router.get("/test-celery")
async def test_celery_connection():
    """Test Celery task execution."""
    try:
        from app.tasks.scraping_tasks import run_spider
        from app.celery_app import celery_app

        # Check if Celery is connected
        inspect = celery_app.control.inspect()
        active_tasks = inspect.active()

        # Try to send a test task
        task = run_spider.delay(
            spider_name="test_spider",
            supermarket_id=1
        )

        return {
            "celery_status": "connected",
            "active_tasks": active_tasks,
            "test_task_id": task.id,
            "test_task_status": task.status
        }

    except Exception as e:
        logger.error(f"Celery test failed: {e}")
        return {
            "celery_status": "error",
            "error": str(e)
        }


@router.get("/{supermarket_id}", response_model=SupermarketResponse)
async def get_supermarket(supermarket_id: int, db: Session = Depends(get_db)):
    """Get a specific supermarket by ID."""
    supermarket = db.query(Supermarket).filter(Supermarket.id == supermarket_id).first()
    if not supermarket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supermarket not found"
        )
    return supermarket


@router.post("/", response_model=SupermarketResponse, status_code=status.HTTP_201_CREATED)
async def create_supermarket(
    supermarket_data: SupermarketCreate,
    db: Session = Depends(get_db)
):
    """Create a new supermarket."""
    # Check if supermarket with same name or domain already exists
    existing = db.query(Supermarket).filter(
        (Supermarket.name == supermarket_data.name) |
        (Supermarket.domain == supermarket_data.domain) |
        (Supermarket.spider_name == supermarket_data.spider_name)
    ).first()
    
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Supermarket with this name, domain, or spider name already exists"
        )
    
    supermarket = Supermarket(
        name=supermarket_data.name,
        domain=supermarket_data.domain,
        base_url=str(supermarket_data.base_url),
        spider_name=supermarket_data.spider_name,
        scraping_config=supermarket_data.scraping_config,
        is_active=supermarket_data.is_active
    )
    
    db.add(supermarket)
    db.commit()
    db.refresh(supermarket)
    
    return supermarket


@router.put("/{supermarket_id}", response_model=SupermarketResponse)
async def update_supermarket(
    supermarket_id: int,
    supermarket_data: SupermarketUpdate,
    db: Session = Depends(get_db)
):
    """Update a supermarket."""
    supermarket = db.query(Supermarket).filter(Supermarket.id == supermarket_id).first()
    if not supermarket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supermarket not found"
        )
    
    # Update fields if provided
    update_data = supermarket_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field == "base_url" and value:
            value = str(value)
        setattr(supermarket, field, value)
    
    db.commit()
    db.refresh(supermarket)
    
    return supermarket


@router.delete("/{supermarket_id}")
async def delete_supermarket(
    supermarket_id: int,
    force: bool = False,
    db: Session = Depends(get_db)
):
    """
    Delete a supermarket and all associated data.
    
    Args:
        supermarket_id: ID of the supermarket to delete
        force: If True, delete even if there are associated products
    """
    supermarket = db.query(Supermarket).filter(Supermarket.id == supermarket_id).first()
    if not supermarket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supermarket not found"
        )
    
    # Check for associated products
    product_count = db.query(Product).filter(Product.supermarket_id == supermarket_id).count()
    
    if product_count > 0 and not force:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot delete supermarket with {product_count} associated products. Use force=true to delete anyway."
        )
    
    # Delete associated data (cascading deletes should handle this, but let's be explicit)
    if product_count > 0:
        # Delete discounts first (they reference products)
        db.query(Discount).filter(
            Discount.product_id.in_(
                db.query(Product.id).filter(Product.supermarket_id == supermarket_id)
            )
        ).delete(synchronize_session=False)
        
        # Delete prices
        db.query(Price).filter(
            Price.product_id.in_(
                db.query(Product.id).filter(Product.supermarket_id == supermarket_id)
            )
        ).delete(synchronize_session=False)
        
        # Delete products
        db.query(Product).filter(Product.supermarket_id == supermarket_id).delete()
    
    # Delete the supermarket
    db.delete(supermarket)
    db.commit()
    
    return {
        "message": f"Supermarket '{supermarket.name}' and {product_count} associated products deleted successfully"
    }


@router.post("/{supermarket_id}/scrape")
async def trigger_scraping(
    supermarket_id: int,
    db: Session = Depends(get_db)
):
    """Trigger scraping for a specific supermarket."""
    supermarket = db.query(Supermarket).filter(Supermarket.id == supermarket_id).first()
    if not supermarket:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Supermarket not found"
        )
    
    if not supermarket.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot scrape inactive supermarket"
        )
    
    # Trigger scraping directly using Celery task
    try:
        from app.tasks.scraping_tasks import run_spider

        task = run_spider.delay(
            spider_name=supermarket.spider_name,
            supermarket_id=supermarket.id
        )
        task_id = task.id

    except Exception as e:
        logger.error(f"Failed to start Celery task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start scraping task: {str(e)}"
        )
    
    return {
        "message": f"Scraping started for {supermarket.name}",
        "task_id": task_id,
        "spider_name": supermarket.spider_name
    }



