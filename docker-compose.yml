version: '3.8'

services:
  # Redis for Celery broker and caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped

  # PostgreSQL database (optional - can use SQLite for development)
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: supermarket_scraper
      POSTGRES_USER: scraper_user
      POSTGRES_PASSWORD: scraper_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  # Main FastAPI application
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************************/supermarket_scraper
      - REDIS_URL=redis://redis:6379/0
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "http://192.168.1.*", "http://10.0.0.*"]
    depends_on:
      - redis
      - postgres
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000

  # Celery worker for background tasks
  celery-worker:
    build: .
    environment:
      - DATABASE_URL=********************************************************/supermarket_scraper
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
      - postgres
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    command: celery -A app.celery_app worker --loglevel=info --queues=default,scraping,maintenance

  # Celery beat scheduler
  celery-beat:
    build: .
    environment:
      - DATABASE_URL=********************************************************/supermarket_scraper
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
      - postgres
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    command: celery -A app.celery_app beat --loglevel=info

  # Flower for Celery monitoring (optional)
  flower:
    build: .
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
    restart: unless-stopped
    command: celery -A app.celery_app flower --port=5555

volumes:
  redis_data:
  postgres_data:
