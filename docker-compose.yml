version: '3.8'

services:
  # Redis for Celery broker and caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: supermarket_scraper
      POSTGRES_USER: scraper_user
      POSTGRES_PASSWORD: scraper_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U scraper_user -d supermarket_scraper"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Database migration service
  migrate:
    build: .
    environment:
      - DATABASE_URL=********************************************************/supermarket_scraper
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    command: alembic upgrade head
    restart: "no"

  # Main FastAPI application
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************************/supermarket_scraper
      - REDIS_URL=redis://redis:6379/0
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - API_DEBUG=false
      - SECRET_KEY=your-production-secret-key-change-this
      - ALLOWED_ORIGINS=["http://localhost:8000", "http://127.0.0.1:8000", "http://192.168.*.*:8000", "http://10.0.*.*:8000", "http://172.16.*.*:8000"]
      - ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
      - ALLOWED_HEADERS=["*"]
      - USER_AGENT=SupermarketScraper/1.0 Docker
      - DOWNLOAD_DELAY=2
      - CONCURRENT_REQUESTS=8
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      migrate:
        condition: service_completed_successfully
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery worker for background tasks
  celery-worker:
    build: .
    environment:
      - DATABASE_URL=********************************************************/supermarket_scraper
      - REDIS_URL=redis://redis:6379/0
      - USER_AGENT=SupermarketScraper/1.0 Docker
      - DOWNLOAD_DELAY=2
      - CONCURRENT_REQUESTS=8
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      api:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    command: celery -A app.celery_app worker --loglevel=info --queues=default,scraping,maintenance

  # Celery beat scheduler
  celery-beat:
    build: .
    environment:
      - DATABASE_URL=********************************************************/supermarket_scraper
      - REDIS_URL=redis://redis:6379/0
      - LOG_LEVEL=INFO
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      api:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    command: celery -A app.celery_app beat --loglevel=info

  # Flower for Celery monitoring
  flower:
    build: .
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      redis:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    command: celery -A app.celery_app flower --port=5555
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5555"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
  postgres_data:
