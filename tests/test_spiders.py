"""
Test suite for Scrapy spiders.
"""
import pytest
from scrapy.http import HtmlResponse, Request
from scrapy.utils.test import get_crawler
from itemloaders import ItemLoader

from scraper.supermarket_scraper.spiders.example_supermarket import ExampleSupermarketSpider
from scraper.supermarket_scraper.items import ProductItem


class TestExampleSupermarketSpider:
    """Test the example supermarket spider."""
    
    @pytest.fixture
    def spider(self):
        """Create spider instance for testing."""
        crawler = get_crawler(ExampleSupermarketSpider)
        spider = ExampleSupermarketSpider.from_crawler(crawler)
        return spider
    
    @pytest.fixture
    def sample_product_html(self):
        """Sample HTML for a product page."""
        return """
        <html>
        <head><title>Test Product</title></head>
        <body>
            <h1 class="product-title">Test Product Name</h1>
            <div class="product-brand">Test Brand</div>
            <div class="product-description">
                <p>This is a test product description.</p>
                <p>It has multiple paragraphs.</p>
            </div>
            <div class="product-sku">SKU: 12345</div>
            <div class="product-size">500g</div>
            <div class="current-price">$4.99</div>
            <div class="regular-price">$5.99</div>
            <div class="product-category">Groceries</div>
            <div class="in-stock">In Stock</div>
            <img class="product-image" src="/images/test-product.jpg" alt="Test Product">
            <div class="promotion-text">Save 17%</div>
        </body>
        </html>
        """
    
    @pytest.fixture
    def sample_category_html(self):
        """Sample HTML for a category page."""
        return """
        <html>
        <body>
            <div class="product-item">
                <a href="/product/1">Product 1</a>
            </div>
            <div class="product-item">
                <a href="/product/2">Product 2</a>
            </div>
            <div class="pagination">
                <a class="next" href="/category?page=2">Next</a>
            </div>
        </body>
        </html>
        """
    
    def create_response(self, html_content, url="https://example-supermarket.com/test"):
        """Create a mock response for testing."""
        return HtmlResponse(
            url=url,
            body=html_content.encode('utf-8'),
            encoding='utf-8'
        )
    
    def test_spider_initialization(self, spider):
        """Test spider is initialized correctly."""
        assert spider.name == 'example_supermarket'
        assert spider.supermarket_name == 'Example Supermarket'
        assert 'example-supermarket.com' in spider.allowed_domains
        assert spider.base_currency == "USD"
    
    def test_parse_category_page(self, spider, sample_category_html):
        """Test parsing category page extracts product links."""
        response = self.create_response(sample_category_html)
        
        results = list(spider.parse(response))
        
        # Should generate requests for product pages
        product_requests = [r for r in results if isinstance(r, Request)]
        assert len(product_requests) >= 2
        
        # Check product URLs
        product_urls = [req.url for req in product_requests]
        assert any('/product/1' in url for url in product_urls)
        assert any('/product/2' in url for url in product_urls)
    
    def test_parse_product_page(self, spider, sample_product_html):
        """Test parsing product page extracts product data."""
        response = self.create_response(sample_product_html)
        
        results = list(spider.parse_product_page(response))
        
        # Should yield at least one product item
        product_items = [r for r in results if isinstance(r, dict)]
        assert len(product_items) >= 1
        
        product = product_items[0]
        assert 'name' in product
        assert 'current_price' in product
        assert 'supermarket_name' in product
    
    def test_extract_product_name(self, spider, sample_product_html):
        """Test product name extraction."""
        response = self.create_response(sample_product_html)
        loader = ItemLoader(item=ProductItem(), response=response)
        
        spider._extract_product_name(loader, response)
        item = loader.load_item()
        
        assert item['name'] == 'Test Product Name'
    
    def test_extract_product_brand(self, spider, sample_product_html):
        """Test product brand extraction."""
        response = self.create_response(sample_product_html)
        loader = ItemLoader(item=ProductItem(), response=response)
        
        spider._extract_product_brand(loader, response)
        item = loader.load_item()
        
        assert item['brand'] == 'Test Brand'
    
    def test_extract_product_description(self, spider, sample_product_html):
        """Test product description extraction."""
        response = self.create_response(sample_product_html)
        loader = ItemLoader(item=ProductItem(), response=response)
        
        spider._extract_product_description(loader, response)
        item = loader.load_item()
        
        description = item['description']
        assert 'test product description' in description.lower()
        assert 'multiple paragraphs' in description.lower()
    
    def test_extract_product_sku(self, spider, sample_product_html):
        """Test product SKU extraction."""
        response = self.create_response(sample_product_html)
        loader = ItemLoader(item=ProductItem(), response=response)
        
        spider._extract_product_sku(loader, response)
        item = loader.load_item()
        
        assert item['sku'] == '12345'
    
    def test_extract_product_prices(self, spider, sample_product_html):
        """Test product price extraction."""
        response = self.create_response(sample_product_html)
        loader = ItemLoader(item=ProductItem(), response=response)
        
        spider._extract_product_prices(loader, response)
        item = loader.load_item()
        
        assert item['current_price'] == 4.99
        assert item['regular_price'] == 5.99
    
    def test_extract_product_size(self, spider, sample_product_html):
        """Test product size extraction."""
        response = self.create_response(sample_product_html)
        loader = ItemLoader(item=ProductItem(), response=response)
        
        spider._extract_product_size(loader, response)
        item = loader.load_item()
        
        assert item['size'] == '500 g'
        assert item['unit'] == 'g'
    
    def test_extract_product_availability(self, spider, sample_product_html):
        """Test product availability extraction."""
        response = self.create_response(sample_product_html)
        loader = ItemLoader(item=ProductItem(), response=response)
        
        spider._extract_product_availability(loader, response)
        item = loader.load_item()
        
        assert item['is_available'] == True
    
    def test_extract_product_images(self, spider, sample_product_html):
        """Test product image extraction."""
        response = self.create_response(sample_product_html)
        loader = ItemLoader(item=ProductItem(), response=response)
        
        spider._extract_product_images(loader, response)
        item = loader.load_item()
        
        assert 'test-product.jpg' in item['image_url']
    
    def test_extract_discount_info(self, spider, sample_product_html):
        """Test discount information extraction."""
        response = self.create_response(sample_product_html)
        loader = ItemLoader(item=ProductItem(), response=response)
        
        spider._extract_discount_info(loader, response)
        item = loader.load_item()
        
        # Should extract promotion text
        # Note: This depends on the discount item structure
        # which might be yielded separately
    
    def test_parse_product_complete(self, spider, sample_product_html):
        """Test complete product parsing."""
        response = self.create_response(sample_product_html)
        
        product = spider.parse_product(response)
        
        assert product is not None
        assert product['name'] == 'Test Product Name'
        assert product['brand'] == 'Test Brand'
        assert product['current_price'] == 4.99
        assert product['regular_price'] == 5.99
        assert product['sku'] == '12345'
        assert product['size'] == '500 g'
        assert product['unit'] == 'g'
        assert product['is_available'] == True
        assert product['supermarket_name'] == 'Example Supermarket'
        assert product['currency'] == 'USD'
    
    def test_spider_closed_stats(self, spider):
        """Test spider statistics tracking."""
        # Simulate some scraping activity
        spider.products_scraped = 10
        spider.discounts_scraped = 2
        spider.errors_count = 1
        
        # Test that closed method doesn't raise errors
        spider.closed("finished")
        
        # Verify stats were recorded
        assert spider.products_scraped == 10
        assert spider.discounts_scraped == 2
        assert spider.errors_count == 1


class TestSpiderUtils:
    """Test spider utility functions."""
    
    def test_price_parsing(self):
        """Test price parsing utility."""
        from scraper.supermarket_scraper.items import parse_price
        
        # Test various price formats
        assert parse_price("$4.99") == 4.99
        assert parse_price("4.99") == 4.99
        assert parse_price("$1,234.56") == 1234.56
        assert parse_price("€5,99") == 5.99  # European format
        assert parse_price("invalid") is None
        assert parse_price("") is None
        assert parse_price(None) is None
    
    def test_text_cleaning(self):
        """Test text cleaning utility."""
        from scraper.supermarket_scraper.items import clean_text
        
        assert clean_text("  Test Product  ") == "Test Product"
        assert clean_text("Test\nProduct") == "Test Product"
        assert clean_text("") == ""
        assert clean_text(None) is None


if __name__ == "__main__":
    pytest.main([__file__])
