"""
Test suite for the API endpoints.
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from app.main import app
from app.db.database import get_db, Base
from app.db.models import Supermarket, Product, Price, Discount

# Test database
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


@pytest.fixture(scope="module")
def setup_database():
    """Set up test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def sample_supermarket():
    """Create a sample supermarket for testing."""
    db = TestingSessionLocal()
    supermarket = Supermarket(
        name="Test Supermarket",
        domain="test.com",
        base_url="https://test.com",
        spider_name="test_spider",
        is_active=True
    )
    db.add(supermarket)
    db.commit()
    db.refresh(supermarket)
    yield supermarket
    db.delete(supermarket)
    db.commit()
    db.close()


class TestHealthEndpoint:
    """Test health check endpoint."""
    
    def test_health_check(self, setup_database):
        """Test health check returns 200."""
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json()["status"] == "healthy"


class TestSupermarketEndpoints:
    """Test supermarket management endpoints."""
    
    def test_list_supermarkets_empty(self, setup_database):
        """Test listing supermarkets when none exist."""
        response = client.get("/api/v1/supermarkets/")
        assert response.status_code == 200
        assert response.json() == []
    
    def test_create_supermarket(self, setup_database):
        """Test creating a new supermarket."""
        supermarket_data = {
            "name": "New Test Supermarket",
            "domain": "newtest.com",
            "base_url": "https://newtest.com",
            "spider_name": "new_test_spider",
            "is_active": True
        }
        
        response = client.post("/api/v1/supermarkets/", json=supermarket_data)
        assert response.status_code == 201
        
        data = response.json()
        assert data["name"] == supermarket_data["name"]
        assert data["domain"] == supermarket_data["domain"]
        assert data["spider_name"] == supermarket_data["spider_name"]
        assert data["is_active"] == True
    
    def test_get_supermarket(self, setup_database, sample_supermarket):
        """Test getting a specific supermarket."""
        response = client.get(f"/api/v1/supermarkets/{sample_supermarket.id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == sample_supermarket.id
        assert data["name"] == sample_supermarket.name
    
    def test_get_nonexistent_supermarket(self, setup_database):
        """Test getting a supermarket that doesn't exist."""
        response = client.get("/api/v1/supermarkets/999")
        assert response.status_code == 404
    
    def test_update_supermarket(self, setup_database, sample_supermarket):
        """Test updating a supermarket."""
        update_data = {"name": "Updated Test Supermarket"}
        
        response = client.put(
            f"/api/v1/supermarkets/{sample_supermarket.id}",
            json=update_data
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["name"] == update_data["name"]
    
    def test_delete_supermarket(self, setup_database, sample_supermarket):
        """Test deleting a supermarket."""
        response = client.delete(f"/api/v1/supermarkets/{sample_supermarket.id}")
        assert response.status_code == 200
        
        # Verify it's deleted
        response = client.get(f"/api/v1/supermarkets/{sample_supermarket.id}")
        assert response.status_code == 404
    
    def test_supermarket_stats(self, setup_database, sample_supermarket):
        """Test getting supermarket statistics."""
        response = client.get("/api/v1/supermarkets/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert len(data) >= 1
        assert any(stat["id"] == sample_supermarket.id for stat in data)


class TestProductEndpoints:
    """Test product management endpoints."""
    
    def test_list_products_empty(self, setup_database):
        """Test listing products when none exist."""
        response = client.get("/api/v1/products/")
        assert response.status_code == 200
        assert response.json() == []
    
    def test_list_products_with_search(self, setup_database):
        """Test searching products."""
        response = client.get("/api/v1/products/?search=milk")
        assert response.status_code == 200
        # Should return empty list since no products exist
        assert response.json() == []
    
    def test_compare_prices_no_products(self, setup_database):
        """Test price comparison when no products exist."""
        response = client.get("/api/v1/products/compare?search=milk")
        assert response.status_code == 404
    
    def test_list_discounts_empty(self, setup_database):
        """Test listing discounts when none exist."""
        response = client.get("/api/v1/products/discounts")
        assert response.status_code == 200
        assert response.json() == []


class TestScrapingEndpoints:
    """Test scraping management endpoints."""
    
    def test_scraping_logs_empty(self, setup_database):
        """Test getting scraping logs when none exist."""
        response = client.get("/api/v1/scraping/logs")
        assert response.status_code == 200
        assert response.json() == []
    
    def test_scraping_stats_empty(self, setup_database):
        """Test getting scraping stats when no logs exist."""
        response = client.get("/api/v1/scraping/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert data["total_runs"] == 0
        assert data["successful_runs"] == 0
        assert data["failed_runs"] == 0
    
    def test_scraping_status(self, setup_database):
        """Test getting scraping status."""
        response = client.get("/api/v1/scraping/status")
        assert response.status_code == 200
        
        data = response.json()
        assert "active_tasks" in data
        assert "running_spiders" in data
        assert "system_status" in data


class TestValidation:
    """Test input validation."""
    
    def test_create_supermarket_missing_fields(self, setup_database):
        """Test creating supermarket with missing required fields."""
        incomplete_data = {
            "name": "Test Supermarket"
            # Missing required fields
        }
        
        response = client.post("/api/v1/supermarkets/", json=incomplete_data)
        assert response.status_code == 422
    
    def test_create_supermarket_invalid_url(self, setup_database):
        """Test creating supermarket with invalid URL."""
        invalid_data = {
            "name": "Test Supermarket",
            "domain": "test.com",
            "base_url": "not-a-valid-url",
            "spider_name": "test_spider"
        }
        
        response = client.post("/api/v1/supermarkets/", json=invalid_data)
        assert response.status_code == 422
    
    def test_create_duplicate_supermarket(self, setup_database, sample_supermarket):
        """Test creating supermarket with duplicate name."""
        duplicate_data = {
            "name": sample_supermarket.name,  # Same name
            "domain": "different.com",
            "base_url": "https://different.com",
            "spider_name": "different_spider"
        }
        
        response = client.post("/api/v1/supermarkets/", json=duplicate_data)
        assert response.status_code == 400


class TestErrorHandling:
    """Test error handling."""
    
    def test_invalid_endpoint(self, setup_database):
        """Test accessing invalid endpoint."""
        response = client.get("/api/v1/invalid-endpoint")
        assert response.status_code == 404
    
    def test_invalid_method(self, setup_database):
        """Test using invalid HTTP method."""
        response = client.patch("/api/v1/supermarkets/")
        assert response.status_code == 405


if __name__ == "__main__":
    pytest.main([__file__])
