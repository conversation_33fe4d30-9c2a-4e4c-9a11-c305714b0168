# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
data/
*.db
*.sqlite
*.sqlite3

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Documentation
docs/
README.md
*.md

# Tests
tests/
pytest.ini

# Scripts (except those needed in container)
scripts/start_local.sh
scripts/deploy_local_network.sh
start.sh

# Temporary files
*.tmp
*.temp
.tmp/
.temp/
