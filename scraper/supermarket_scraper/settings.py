"""
Scrapy settings for supermarket_scraper project.
"""
import os
import sys

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from app.core.config import settings as app_settings

BOT_NAME = 'supermarket_scraper'

SPIDER_MODULES = ['supermarket_scraper.spiders']
NEWSPIDER_MODULE = 'supermarket_scraper.spiders'

# Obey robots.txt rules
ROBOTSTXT_OBEY = True

# Configure delays and concurrency
DOWNLOAD_DELAY = app_settings.download_delay
RANDOMIZE_DOWNLOAD_DELAY = app_settings.randomize_download_delay
CONCURRENT_REQUESTS = app_settings.concurrent_requests
CONCURRENT_REQUESTS_PER_DOMAIN = app_settings.concurrent_requests_per_domain

# User agent
USER_AGENT = app_settings.user_agent

# Configure pipelines
ITEM_PIPELINES = {
    'supermarket_scraper.pipelines.ValidationPipeline': 100,
    'supermarket_scraper.pipelines.DuplicatesPipeline': 200,
    'supermarket_scraper.pipelines.DatabasePipeline': 300,
}

# Configure middlewares
DOWNLOADER_MIDDLEWARES = {
    'supermarket_scraper.middlewares.RotateUserAgentMiddleware': 400,
    'supermarket_scraper.middlewares.RetryMiddleware': 500,
}

# AutoThrottle settings
AUTOTHROTTLE_ENABLED = True
AUTOTHROTTLE_START_DELAY = 1
AUTOTHROTTLE_MAX_DELAY = 10
AUTOTHROTTLE_TARGET_CONCURRENCY = 2.0
AUTOTHROTTLE_DEBUG = False

# Cache settings
HTTPCACHE_ENABLED = True
HTTPCACHE_EXPIRATION_SECS = 3600
HTTPCACHE_DIR = 'httpcache'
HTTPCACHE_IGNORE_HTTP_CODES = [503, 504, 505, 500, 403, 404, 408, 429]

# Request fingerprinting
REQUEST_FINGERPRINTER_IMPLEMENTATION = '2.7'

# Twisted reactor
TWISTED_REACTOR = 'twisted.internet.asyncioreactor.AsyncioSelectorReactor'

# Feed exports
FEED_EXPORT_ENCODING = 'utf-8'

# Logging
LOG_LEVEL = app_settings.log_level
# Disable file logging to avoid path issues
# LOG_FILE = app_settings.log_file

# Retry settings
RETRY_TIMES = 3
RETRY_HTTP_CODES = [500, 502, 503, 504, 408, 429]

# Download timeout
DOWNLOAD_TIMEOUT = 30

# Headers
DEFAULT_REQUEST_HEADERS = {
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'en',
    'Accept-Encoding': 'gzip, deflate',
    'DNT': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# Selenium settings (for JavaScript-heavy sites) - DISABLED FOR TESTING
# if app_settings.selenium_driver_path:
#     SELENIUM_DRIVER_NAME = 'chrome'
#     SELENIUM_DRIVER_EXECUTABLE_PATH = app_settings.selenium_driver_path
#     SELENIUM_DRIVER_ARGUMENTS = [
#         '--headless' if app_settings.selenium_headless else '',
#         '--no-sandbox',
#         '--disable-dev-shm-usage',
#         '--disable-gpu',
#         '--window-size=1920,1080',
#     ]
#
#     DOWNLOADER_MIDDLEWARES.update({
#         'scrapy_selenium.SeleniumMiddleware': 800
#     })

# Custom settings for different environments
if os.getenv('SCRAPY_ENV') == 'development':
    # Development settings
    HTTPCACHE_ENABLED = False
    LOG_LEVEL = 'DEBUG'
    DOWNLOAD_DELAY = 0.5
    
elif os.getenv('SCRAPY_ENV') == 'production':
    # Production settings
    DOWNLOAD_DELAY = 2
    CONCURRENT_REQUESTS = 8
    CONCURRENT_REQUESTS_PER_DOMAIN = 4
    AUTOTHROTTLE_TARGET_CONCURRENCY = 1.0
