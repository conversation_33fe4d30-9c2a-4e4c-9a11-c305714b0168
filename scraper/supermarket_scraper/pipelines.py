"""
Scrapy pipelines for processing scraped items.
"""
import logging
from datetime import datetime, timezone
from typing import Dict, Any
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError
from itemadapter import ItemAdapter

from app.db.database import engine
from app.db.models import Supermarket, Product, Price, Discount, Category

logger = logging.getLogger(__name__)


class ValidationPipeline:
    """Pipeline to validate scraped items."""
    
    def process_item(self, item, spider):
        """Validate item data."""
        adapter = ItemAdapter(item)
        
        # Validate required fields based on item type
        if 'ProductItem' in item.__class__.__name__:
            required_fields = ['name', 'supermarket_name']
            for field in required_fields:
                if not adapter.get(field):
                    raise ValueError(f"Missing required field: {field}")
                    
            # Validate price fields
            regular_price = adapter.get('regular_price')
            current_price = adapter.get('current_price')
            
            if regular_price is not None and regular_price < 0:
                raise ValueError("Regular price cannot be negative")
            if current_price is not None and current_price < 0:
                raise ValueError("Current price cannot be negative")
                
        elif 'DiscountItem' in item.__class__.__name__:
            required_fields = ['product_name', 'supermarket_name', 'discount_type']
            for field in required_fields:
                if not adapter.get(field):
                    raise ValueError(f"Missing required field: {field}")
        
        return item


class DuplicatesPipeline:
    """Pipeline to filter duplicate items."""
    
    def __init__(self):
        self.seen_products = set()
        self.seen_discounts = set()
    
    def process_item(self, item, spider):
        """Filter duplicate items."""
        adapter = ItemAdapter(item)
        
        if 'ProductItem' in item.__class__.__name__:
            # Create unique identifier for product
            identifier = (
                adapter.get('name', '').lower(),
                adapter.get('supermarket_name', '').lower(),
                adapter.get('sku', '')
            )
            
            if identifier in self.seen_products:
                logger.debug(f"Duplicate product filtered: {adapter.get('name')}")
                raise ValueError("Duplicate product")
            
            self.seen_products.add(identifier)
            
        elif 'DiscountItem' in item.__class__.__name__:
            # Create unique identifier for discount
            identifier = (
                adapter.get('product_name', '').lower(),
                adapter.get('supermarket_name', '').lower(),
                adapter.get('promotion_text', '').lower()
            )
            
            if identifier in self.seen_discounts:
                logger.debug(f"Duplicate discount filtered: {adapter.get('promotion_text')}")
                raise ValueError("Duplicate discount")
            
            self.seen_discounts.add(identifier)
        
        return item


class DatabasePipeline:
    """Pipeline to save items to database."""
    
    def __init__(self):
        self.Session = sessionmaker(bind=engine)
        self.session = None
        self.supermarket_cache = {}
        self.category_cache = {}
    
    def open_spider(self, spider):
        """Initialize database session when spider opens."""
        self.session = self.Session()
        logger.info(f"Database pipeline opened for spider: {spider.name}")
        
        # Cache supermarkets and categories
        self._load_caches()
    
    def close_spider(self, spider):
        """Close database session when spider closes."""
        if self.session:
            self.session.close()
        logger.info(f"Database pipeline closed for spider: {spider.name}")
    
    def _load_caches(self):
        """Load supermarkets and categories into cache."""
        # Load supermarkets
        supermarkets = self.session.query(Supermarket).all()
        for supermarket in supermarkets:
            self.supermarket_cache[supermarket.name.lower()] = supermarket
        
        # Load categories
        categories = self.session.query(Category).all()
        for category in categories:
            self.category_cache[category.name.lower()] = category
    
    def _get_or_create_supermarket(self, name: str, spider_name: str, spider=None) -> Supermarket:
        """Get or create supermarket."""
        name_lower = name.lower()
        
        if name_lower in self.supermarket_cache:
            return self.supermarket_cache[name_lower]
        
        # Create new supermarket
        domain = 'unknown.com'
        base_url = ''

        if spider:
            domain = getattr(spider, 'allowed_domains', ['unknown.com'])[0]
            base_url = getattr(spider, 'start_urls', [''])[0]

        supermarket = Supermarket(
            name=name,
            domain=domain,
            base_url=base_url,
            spider_name=spider_name
        )
        
        try:
            self.session.add(supermarket)
            self.session.commit()
            self.supermarket_cache[name_lower] = supermarket
            logger.info(f"Created new supermarket: {name}")
        except IntegrityError:
            self.session.rollback()
            # Try to get existing supermarket
            supermarket = self.session.query(Supermarket).filter_by(name=name).first()
            if supermarket:
                self.supermarket_cache[name_lower] = supermarket
        
        return supermarket
    
    def _get_or_create_category(self, name: str) -> Category:
        """Get or create category."""
        if not name:
            return None
            
        name_lower = name.lower()
        
        if name_lower in self.category_cache:
            return self.category_cache[name_lower]
        
        # Create new category
        category = Category(
            name=name,
            slug=name.lower().replace(' ', '-').replace('&', 'and')
        )
        
        try:
            self.session.add(category)
            self.session.commit()
            self.category_cache[name_lower] = category
            logger.info(f"Created new category: {name}")
        except IntegrityError:
            self.session.rollback()
            # Try to get existing category
            category = self.session.query(Category).filter_by(name=name).first()
            if category:
                self.category_cache[name_lower] = category
        
        return category

    def process_item(self, item, spider):
        """Process and save item to database."""
        adapter = ItemAdapter(item)

        try:
            if 'ProductItem' in item.__class__.__name__:
                self._save_product(adapter, spider)
            elif 'DiscountItem' in item.__class__.__name__:
                self._save_discount(adapter, spider)

            return item

        except Exception as e:
            logger.error(f"Error saving item to database: {e}")
            self.session.rollback()
            raise

    def _save_product(self, adapter: ItemAdapter, spider):
        """Save product item to database."""
        supermarket = self._get_or_create_supermarket(
            adapter.get('supermarket_name'),
            spider.name,
            spider
        )

        category = self._get_or_create_category(adapter.get('category'))

        # Check if product already exists
        existing_product = self.session.query(Product).filter_by(
            supermarket_id=supermarket.id,
            name=adapter.get('name'),
            sku=adapter.get('sku')
        ).first()

        if existing_product:
            # Update existing product
            product = existing_product
            product.description = adapter.get('description') or product.description
            product.brand = adapter.get('brand') or product.brand
            product.size = adapter.get('size') or product.size
            product.unit = adapter.get('unit') or product.unit
            product.product_url = adapter.get('product_url') or product.product_url
            product.image_url = adapter.get('image_url') or product.image_url
            product.is_available = adapter.get('is_available', True)
            product.last_seen_at = datetime.now(timezone.utc)
            if category:
                product.category_id = category.id
        else:
            # Create new product
            product = Product(
                name=adapter.get('name'),
                description=adapter.get('description'),
                brand=adapter.get('brand'),
                sku=adapter.get('sku'),
                barcode=adapter.get('barcode'),
                size=adapter.get('size'),
                unit=adapter.get('unit'),
                supermarket_id=supermarket.id,
                category_id=category.id if category else None,
                product_url=adapter.get('product_url'),
                image_url=adapter.get('image_url'),
                is_available=adapter.get('is_available', True)
            )
            self.session.add(product)

        # Save price information if available
        regular_price = adapter.get('regular_price')
        current_price = adapter.get('current_price')

        if regular_price is not None or current_price is not None:
            # Mark previous prices as not current
            self.session.query(Price).filter_by(
                product_id=product.id,
                is_current=True
            ).update({'is_current': False})

            # Create new price record
            price = Price(
                product_id=product.id,
                regular_price=regular_price or current_price,
                current_price=current_price or regular_price,
                currency=adapter.get('currency', 'USD'),
                is_current=True
            )
            self.session.add(price)

        self.session.commit()
        logger.debug(f"Saved product: {adapter.get('name')}")

    def _save_discount(self, adapter: ItemAdapter, spider):
        """Save discount item to database."""
        supermarket = self._get_or_create_supermarket(
            adapter.get('supermarket_name'),
            spider.name,
            spider
        )

        # Find the product this discount applies to
        product = self.session.query(Product).filter_by(
            supermarket_id=supermarket.id,
            name=adapter.get('product_name')
        ).first()

        if not product:
            logger.warning(f"Product not found for discount: {adapter.get('product_name')}")
            return

        # Check if discount already exists
        existing_discount = self.session.query(Discount).filter_by(
            product_id=product.id,
            promotion_text=adapter.get('promotion_text'),
            is_active=True
        ).first()

        if existing_discount:
            # Update existing discount
            discount = existing_discount
            discount.discount_value = adapter.get('discount_value') or discount.discount_value
            discount.original_price = adapter.get('original_price') or discount.original_price
            discount.discounted_price = adapter.get('discounted_price') or discount.discounted_price
            discount.promotion_code = adapter.get('promotion_code') or discount.promotion_code
            discount.valid_from = adapter.get('valid_from') or discount.valid_from
            discount.valid_until = adapter.get('valid_until') or discount.valid_until
        else:
            # Create new discount
            discount = Discount(
                product_id=product.id,
                discount_type=adapter.get('discount_type'),
                discount_value=adapter.get('discount_value'),
                original_price=adapter.get('original_price'),
                discounted_price=adapter.get('discounted_price'),
                promotion_text=adapter.get('promotion_text'),
                promotion_code=adapter.get('promotion_code'),
                valid_from=adapter.get('valid_from'),
                valid_until=adapter.get('valid_until'),
                is_active=True
            )
            self.session.add(discount)

        self.session.commit()
        logger.debug(f"Saved discount: {adapter.get('promotion_text')}")
