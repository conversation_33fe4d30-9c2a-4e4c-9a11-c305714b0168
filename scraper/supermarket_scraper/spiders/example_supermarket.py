"""
Example spider for a generic supermarket website.
This serves as a template for creating new supermarket scrapers.
"""
import re
from urllib.parse import urljoin
from itemloaders import ItemLoader
from scrapy import Request
from scrapy.http import Response

from .base import SupermarketSpider
from ..items import ProductItem


class ExampleSupermarketSpider(SupermarketSpider):
    """
    Example spider for scraping a generic supermarket website.
    
    This spider demonstrates the structure and methods needed
    to scrape product information from a supermarket website.
    """
    
    name = 'example_supermarket'
    supermarket_name = 'Example Supermarket'
    allowed_domains = ['httpbin.org']
    start_urls = [
        'https://httpbin.org/html',  # Test URL that returns HTML
    ]
    base_currency = "USD"
    
    def parse(self, response: Response):
        """Parse test page and create a test product."""
        self.logger.info(f"Parsing response from {response.url}")
        self.logger.info(f"Response status: {response.status}")

        # Create a test product to verify the pipeline works
        loader = ItemLoader(item=ProductItem(), response=response)

        # Add test data
        loader.add_value('supermarket_name', self.supermarket_name)
        loader.add_value('product_url', response.url)
        loader.add_value('currency', self.base_currency)
        loader.add_value('name', 'Test Product - Example Supermarket')
        loader.add_value('description', 'This is a test product to verify scraping infrastructure')
        loader.add_value('brand', 'Test Brand')
        loader.add_value('sku', 'TEST-001')
        loader.add_value('category', 'Test Category')
        loader.add_value('size', '1 unit')
        loader.add_value('unit', 'each')
        loader.add_value('regular_price', '10.00')
        loader.add_value('current_price', '9.50')
        loader.add_value('is_available', True)
        loader.add_value('image_url', 'https://httpbin.org/image/png')

        item = loader.load_item()
        self.products_scraped += 1

        self.logger.info(f"Created test product: {item.get('name')}")
        yield item
        
        # Extract category links for pagination
        category_links = response.css('.category-nav a::attr(href)').getall()
        for link in category_links:
            category_url = urljoin(response.url, link)
            yield Request(
                url=category_url,
                callback=self.parse,
                meta={'supermarket_name': self.supermarket_name}
            )
        
        # Handle pagination
        next_page = response.css('.pagination .next::attr(href)').get()
        if next_page:
            yield Request(
                url=urljoin(response.url, next_page),
                callback=self.parse,
                meta={'supermarket_name': self.supermarket_name}
            )
    
    def parse_product_page(self, response: Response):
        """Parse individual product page."""
        product = self.parse_product(response)
        if product:
            yield product
        
        # Check for discount information on the same page
        discount = self.parse_discount(response, product.get('name') if product else None)
        if discount:
            yield discount
    
    # Implementation of required extraction methods
    
    def _extract_product_name(self, loader: ItemLoader, response: Response):
        """Extract product name."""
        # Try multiple selectors for product name
        name_selectors = [
            'h1.product-title::text',
            '.product-name::text',
            'h1::text',
            '.product-header h1::text'
        ]
        
        for selector in name_selectors:
            name = response.css(selector).get()
            if name:
                loader.add_value('name', name.strip())
                break
    
    def _extract_product_description(self, loader: ItemLoader, response: Response):
        """Extract product description."""
        description_selectors = [
            '.product-description::text',
            '.product-details p::text',
            '.description::text'
        ]
        
        for selector in description_selectors:
            description = response.css(selector).getall()
            if description:
                loader.add_value('description', ' '.join(description))
                break
    
    def _extract_product_brand(self, loader: ItemLoader, response: Response):
        """Extract product brand."""
        brand_selectors = [
            '.product-brand::text',
            '.brand-name::text',
            '[data-brand]::attr(data-brand)'
        ]
        
        for selector in brand_selectors:
            brand = response.css(selector).get()
            if brand:
                loader.add_value('brand', brand.strip())
                break
    
    def _extract_product_sku(self, loader: ItemLoader, response: Response):
        """Extract product SKU."""
        sku_selectors = [
            '.product-sku::text',
            '[data-sku]::attr(data-sku)',
            '.sku::text'
        ]
        
        for selector in sku_selectors:
            sku = response.css(selector).get()
            if sku:
                # Clean SKU (remove "SKU:" prefix if present)
                sku_clean = re.sub(r'^SKU:\s*', '', sku.strip(), flags=re.IGNORECASE)
                loader.add_value('sku', sku_clean)
                break
    
    def _extract_product_category(self, loader: ItemLoader, response: Response):
        """Extract product category."""
        category_selectors = [
            '.breadcrumb li:last-child::text',
            '.product-category::text',
            '.category::text'
        ]
        
        for selector in category_selectors:
            category = response.css(selector).get()
            if category:
                loader.add_value('category', category.strip())
                break
    
    def _extract_product_size(self, loader: ItemLoader, response: Response):
        """Extract product size and unit."""
        size_selectors = [
            '.product-size::text',
            '.size::text',
            '.weight::text'
        ]
        
        for selector in size_selectors:
            size = response.css(selector).get()
            if size:
                loader.add_value('size', size.strip())
                
                # Extract unit from size if possible
                unit_match = re.search(r'(kg|g|lb|oz|L|ml|pack|each)', size, re.IGNORECASE)
                if unit_match:
                    loader.add_value('unit', unit_match.group(1))
                break
    
    def _extract_product_prices(self, loader: ItemLoader, response: Response):
        """Extract product prices."""
        # Regular price
        regular_price_selectors = [
            '.regular-price::text',
            '.original-price::text',
            '.price-regular::text'
        ]
        
        for selector in regular_price_selectors:
            price = response.css(selector).get()
            if price:
                loader.add_value('regular_price', price)
                break
        
        # Current/sale price
        current_price_selectors = [
            '.current-price::text',
            '.sale-price::text',
            '.price::text',
            '.product-price::text'
        ]
        
        for selector in current_price_selectors:
            price = response.css(selector).get()
            if price:
                loader.add_value('current_price', price)
                break
    
    def _extract_product_availability(self, loader: ItemLoader, response: Response):
        """Extract product availability."""
        # Check for out of stock indicators
        out_of_stock_selectors = [
            '.out-of-stock',
            '.unavailable',
            '.sold-out'
        ]
        
        is_available = True
        for selector in out_of_stock_selectors:
            if response.css(selector):
                is_available = False
                break
        
        # Check for in stock indicators
        if is_available:
            in_stock_selectors = [
                '.in-stock',
                '.available',
                '.add-to-cart'
            ]
            
            found_in_stock = False
            for selector in in_stock_selectors:
                if response.css(selector):
                    found_in_stock = True
                    break
            
            # If no explicit indicators found, assume available
            is_available = found_in_stock or True
        
        loader.add_value('is_available', is_available)
    
    def _extract_product_images(self, loader: ItemLoader, response: Response):
        """Extract product images."""
        image_selectors = [
            '.product-image img::attr(src)',
            '.product-photo img::attr(src)',
            '.main-image::attr(src)'
        ]
        
        for selector in image_selectors:
            image = response.css(selector).get()
            if image:
                image_url = urljoin(response.url, image)
                loader.add_value('image_url', image_url)
                break
    
    def _extract_discount_info(self, loader: ItemLoader, response: Response):
        """Extract discount information."""
        # Look for discount/promotion text
        discount_selectors = [
            '.promotion-text::text',
            '.discount-info::text',
            '.sale-badge::text'
        ]
        
        for selector in discount_selectors:
            discount_text = response.css(selector).get()
            if discount_text:
                loader.add_value('promotion_text', discount_text.strip())
                
                # Try to extract discount type and value
                if 'off' in discount_text.lower():
                    if '%' in discount_text:
                        loader.add_value('discount_type', 'percentage')
                        # Extract percentage value
                        percent_match = re.search(r'(\d+)%', discount_text)
                        if percent_match:
                            loader.add_value('discount_value', float(percent_match.group(1)))
                    else:
                        loader.add_value('discount_type', 'fixed_amount')
                break
    
    def _extract_discount_validity(self, loader: ItemLoader, response: Response):
        """Extract discount validity period."""
        # Look for validity dates
        validity_selectors = [
            '.promotion-dates::text',
            '.valid-until::text',
            '.expires::text'
        ]
        
        for selector in validity_selectors:
            validity = response.css(selector).get()
            if validity:
                # This would need more sophisticated date parsing
                # For now, just store the raw text
                loader.add_value('valid_until', validity.strip())
                break
