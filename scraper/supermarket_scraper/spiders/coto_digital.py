"""
Simple test spider for Coto Digital.
This is a basic implementation to test the scraping infrastructure.
"""
import re
from urllib.parse import urljoin
from itemloaders import ItemLoader
from scrapy import Request
from scrapy.http import Response

from .base import SupermarketSpider
from ..items import ProductItem


class CotoDigitalSpider(SupermarketSpider):
    """
    Test spider for Coto Digital website.
    
    This is a simplified implementation for testing purposes.
    It will attempt to scrape from a test URL to verify the infrastructure works.
    """
    
    name = 'coto_digital'
    supermarket_name = 'Coto'
    allowed_domains = ['httpbin.org']  # Using httpbin for testing
    start_urls = [
        'https://httpbin.org/html',  # Test URL that returns HTML
    ]
    base_currency = "ARS"
    
    custom_settings = {
        'DOWNLOAD_DELAY': 1,
        'RANDOMIZE_DOWNLOAD_DELAY': False,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
        'ROBOTSTXT_OBEY': False,  # Disable for testing
    }
    
    def parse(self, response: Response):
        """Parse test page and create a dummy product."""
        self.logger.info(f"Parsing response from {response.url}")
        self.logger.info(f"Response status: {response.status}")
        self.logger.info(f"Response body length: {len(response.body)}")
        
        # Create a test product to verify the pipeline works
        loader = ItemLoader(item=ProductItem(), response=response)
        
        # Add test data
        loader.add_value('supermarket_name', self.supermarket_name)
        loader.add_value('product_url', response.url)
        loader.add_value('currency', self.base_currency)
        loader.add_value('name', 'Test Product - Coto Digital')
        loader.add_value('description', 'This is a test product to verify scraping infrastructure')
        loader.add_value('brand', 'Test Brand')
        loader.add_value('sku', 'TEST-001')
        loader.add_value('category', 'Test Category')
        loader.add_value('size', '1 unit')
        loader.add_value('unit', 'each')
        loader.add_value('regular_price', '100.00')
        loader.add_value('current_price', '95.00')
        loader.add_value('is_available', True)
        loader.add_value('image_url', 'https://httpbin.org/image/png')
        
        item = loader.load_item()
        self.products_scraped += 1
        
        self.logger.info(f"Created test product: {item.get('name')}")
        yield item
    
    # Required method implementations (simplified for testing)
    
    def _extract_product_name(self, loader: ItemLoader, response: Response):
        """Extract product name - test implementation."""
        loader.add_value('name', 'Test Product Name')
    
    def _extract_product_description(self, loader: ItemLoader, response: Response):
        """Extract product description - test implementation."""
        loader.add_value('description', 'Test product description')
    
    def _extract_product_brand(self, loader: ItemLoader, response: Response):
        """Extract product brand - test implementation."""
        loader.add_value('brand', 'Test Brand')
    
    def _extract_product_sku(self, loader: ItemLoader, response: Response):
        """Extract product SKU - test implementation."""
        loader.add_value('sku', 'TEST-SKU')
    
    def _extract_product_category(self, loader: ItemLoader, response: Response):
        """Extract product category - test implementation."""
        loader.add_value('category', 'Test Category')
    
    def _extract_product_size(self, loader: ItemLoader, response: Response):
        """Extract product size - test implementation."""
        loader.add_value('size', '1 unit')
        loader.add_value('unit', 'each')
    
    def _extract_product_prices(self, loader: ItemLoader, response: Response):
        """Extract product prices - test implementation."""
        loader.add_value('regular_price', '100.00')
        loader.add_value('current_price', '95.00')
    
    def _extract_product_availability(self, loader: ItemLoader, response: Response):
        """Extract product availability - test implementation."""
        loader.add_value('is_available', True)
    
    def _extract_product_images(self, loader: ItemLoader, response: Response):
        """Extract product images - test implementation."""
        loader.add_value('image_url', 'https://httpbin.org/image/png')
    
    def _extract_discount_info(self, loader: ItemLoader, response: Response):
        """Extract discount information - test implementation."""
        loader.add_value('promotion_text', 'Test promotion')
        loader.add_value('discount_type', 'percentage')
        loader.add_value('discount_value', 5.0)
    
    def _extract_discount_validity(self, loader: ItemLoader, response: Response):
        """Extract discount validity - test implementation."""
        loader.add_value('valid_until', '2024-12-31')
