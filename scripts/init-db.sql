-- Initialize database for supermarket scraper
-- This script runs automatically when PostgreSQL container starts

-- Ensure the database exists (it should be created by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS supermarket_scraper;

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON DATABASE supermarket_scraper TO scraper_user;

-- Create extensions if needed
-- CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- The actual tables will be created by Alembic migrations
