#!/bin/bash

# Deploy Supermarket Scraper for local network access

set -e

echo "Deploying Supermarket Price Comparison Scraper for local network..."

# Get local IP address
LOCAL_IP=$(hostname -I | awk '{print $1}')
echo "Local IP address: $LOCAL_IP"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file for production
cat > .env << EOF
# Database Configuration
DATABASE_URL=********************************************************/supermarket_scraper
DATABASE_ECHO=false

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false
SECRET_KEY=$(openssl rand -hex 32)

# CORS Configuration - Allow local network access
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "http://${LOCAL_IP}:8000", "http://192.168.*.*:8000", "http://10.0.*.*:8000"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# Scraping Configuration
USER_AGENT=SupermarketScraper/1.0 (+http://${LOCAL_IP}:8000)
DOWNLOAD_DELAY=2
RANDOMIZE_DOWNLOAD_DELAY=true
CONCURRENT_REQUESTS=8
CONCURRENT_REQUESTS_PER_DOMAIN=4

# Selenium Configuration
SELENIUM_DRIVER_PATH=/usr/local/bin/chromedriver
SELENIUM_HEADLESS=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log

# Scheduling Configuration
SCRAPING_SCHEDULE_HOURS=6
CLEANUP_SCHEDULE_DAYS=30
EOF

echo "Created .env file with local network configuration"

# Create necessary directories
mkdir -p logs data

# Build and start services
echo "Building Docker images..."
docker-compose build

echo "Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 10

# Run database migrations
echo "Running database migrations..."
docker-compose exec api alembic upgrade head

# Check service status
echo "Checking service status..."
docker-compose ps

echo ""
echo "Deployment completed successfully!"
echo ""
echo "Access the application at:"
echo "- Local: http://localhost:8000"
echo "- Network: http://${LOCAL_IP}:8000"
echo "- API Documentation: http://${LOCAL_IP}:8000/docs"
echo "- Flower Monitoring: http://${LOCAL_IP}:5555"
echo ""
echo "To stop the services, run: docker-compose down"
echo "To view logs, run: docker-compose logs -f"
echo ""

# Display network access instructions
echo "Network Access Instructions:"
echo "1. Make sure your firewall allows connections on ports 8000 and 5555"
echo "2. Other devices on your network can access the application using:"
echo "   http://${LOCAL_IP}:8000"
echo "3. To find devices on your network, you can use:"
echo "   nmap -sn ${LOCAL_IP%.*}.0/24"
echo ""
