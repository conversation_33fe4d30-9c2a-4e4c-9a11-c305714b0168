#!/bin/bash

# Start Supermarket Scraper locally for development

set -e

echo "Starting Supermarket Price Comparison Scraper..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "Creating .env file from template..."
    cp .env.example .env
    echo "Please edit .env file with your configuration before continuing."
    exit 1
fi

# Create necessary directories
mkdir -p logs data

# Run database migrations
echo "Running database migrations..."
alembic upgrade head

# Start Redis (if not running)
if ! pgrep -x "redis-server" > /dev/null; then
    echo "Starting Redis server..."
    redis-server --daemonize yes --port 6379
fi

# Start services in background
echo "Starting FastAPI server..."
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload &
API_PID=$!

echo "Starting Celery worker..."
celery -A app.celery_app worker --loglevel=info --queues=default,scraping,maintenance &
WORKER_PID=$!

echo "Starting Celery beat scheduler..."
celery -A app.celery_app beat --loglevel=info &
BEAT_PID=$!

echo "Starting Flower monitoring (optional)..."
celery -A app.celery_app flower --port=5555 &
FLOWER_PID=$!

# Function to cleanup on exit
cleanup() {
    echo "Shutting down services..."
    kill $API_PID $WORKER_PID $BEAT_PID $FLOWER_PID 2>/dev/null || true
    wait
    echo "All services stopped."
}

# Set trap to cleanup on script exit
trap cleanup EXIT

echo ""
echo "Services started successfully!"
echo "- API Server: http://localhost:8000"
echo "- API Documentation: http://localhost:8000/docs"
echo "- Flower Monitoring: http://localhost:5555"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user interrupt
wait
