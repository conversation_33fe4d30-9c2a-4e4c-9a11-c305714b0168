#!/bin/bash

# Docker entrypoint script for the API service
# This ensures proper initialization when the container starts

set -e

echo "🚀 Starting Supermarket Scraper API..."

# Wait for database to be ready
echo "⏳ Waiting for database..."
while ! nc -z postgres 5432; do
    echo "Database not ready, waiting..."
    sleep 2
done
echo "✅ Database is ready"

# Wait for Redis to be ready
echo "⏳ Waiting for Redis..."
while ! nc -z redis 6379; do
    echo "Redis not ready, waiting..."
    sleep 2
done
echo "✅ Redis is ready"

# Create necessary directories
mkdir -p /app/logs /app/data
chmod 755 /app/logs /app/data

# Run database migrations
echo "🔄 Running database migrations..."
alembic upgrade head

echo "✅ Initialization complete"

# Start the application
echo "🌐 Starting FastAPI application..."
exec "$@"
