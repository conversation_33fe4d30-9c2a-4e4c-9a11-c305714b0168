#!/bin/bash

# Docker startup script for supermarket scraper
# This script prepares the environment and starts the application

set -e

echo "🚀 Starting Supermarket Price Comparison Scraper..."

# Get local IP address for network access
LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "localhost")
echo "📍 Local IP detected: $LOCAL_IP"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs data

# Set proper permissions
chmod 755 logs data

# Check if .env exists, if not create from template
if [ ! -f ".env" ]; then
    echo "⚙️  Creating .env file from template..."
    cp .env.example .env
    
    # Update .env with Docker-specific settings
    cat > .env << EOF
# Database Configuration
DATABASE_URL=********************************************************/supermarket_scraper
DATABASE_ECHO=false

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false
SECRET_KEY=$(openssl rand -hex 32 2>/dev/null || echo "change-this-secret-key-in-production")

# CORS Configuration - Allow local network access
ALLOWED_ORIGINS=["http://localhost:8000", "http://127.0.0.1:8000", "http://${LOCAL_IP}:8000", "http://192.168.*.*:8000", "http://10.0.*.*:8000", "http://172.16.*.*:8000"]
ALLOWED_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
ALLOWED_HEADERS=["*"]

# Scraping Configuration
USER_AGENT=SupermarketScraper/1.0 Docker
DOWNLOAD_DELAY=2
RANDOMIZE_DOWNLOAD_DELAY=true
CONCURRENT_REQUESTS=8
CONCURRENT_REQUESTS_PER_DOMAIN=4

# Selenium Configuration
SELENIUM_DRIVER_PATH=/usr/local/bin/chromedriver
SELENIUM_HEADLESS=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log

# Scheduling Configuration
SCRAPING_SCHEDULE_HOURS=6
CLEANUP_SCHEDULE_DAYS=30
EOF
    echo "✅ .env file created with Docker configuration"
else
    echo "✅ .env file already exists"
fi

# Start Docker Compose
echo "🐳 Starting Docker containers..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 15

# Check service health
echo "🔍 Checking service health..."

# Check if API is responding
for i in {1..30}; do
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ API service is healthy"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ API service failed to start"
        docker-compose logs api
        exit 1
    fi
    echo "⏳ Waiting for API service... ($i/30)"
    sleep 2
done

# Check if Flower is responding
for i in {1..15}; do
    if curl -f http://localhost:5555 >/dev/null 2>&1; then
        echo "✅ Flower monitoring is healthy"
        break
    fi
    if [ $i -eq 15 ]; then
        echo "⚠️  Flower monitoring may not be ready yet"
        break
    fi
    echo "⏳ Waiting for Flower... ($i/15)"
    sleep 2
done

# Display service status
echo ""
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "🌐 Access the application at:"
echo "   • Local: http://localhost:8000"
echo "   • Network: http://${LOCAL_IP}:8000"
echo "   • API Documentation: http://${LOCAL_IP}:8000/docs"
echo "   • Flower Monitoring: http://${LOCAL_IP}:8000:5555"
echo ""
echo "📱 Network Access Instructions:"
echo "   1. Make sure your firewall allows connections on ports 8000 and 5555"
echo "   2. Other devices on your network can access the application using:"
echo "      http://${LOCAL_IP}:8000"
echo "   3. To find other devices on your network:"
echo "      nmap -sn ${LOCAL_IP%.*}.0/24"
echo ""
echo "🔧 Management Commands:"
echo "   • View logs: docker-compose logs -f"
echo "   • Stop services: docker-compose down"
echo "   • Restart services: docker-compose restart"
echo "   • Update and restart: docker-compose up -d --build"
echo ""
echo "📚 Documentation:"
echo "   • Adding sites: docs/adding-sites.md"
echo "   • API reference: docs/api-documentation.md"
echo "   • Deployment guide: docs/deployment-guide.md"
echo ""
