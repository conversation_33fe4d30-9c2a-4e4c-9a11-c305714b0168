# API Documentation

This document provides comprehensive documentation for the Supermarket Price Comparison API.

## Base URL

- Local Development: `http://localhost:8000`
- Local Network: `http://YOUR_LOCAL_IP:8000`

## Authentication

Currently, the API does not require authentication. This may be added in future versions.

## API Endpoints

### Supermarkets

#### List Supermarkets
```
GET /api/v1/supermarkets/
```

Query Parameters:
- `skip` (int): Number of records to skip (default: 0)
- `limit` (int): Maximum number of records to return (default: 100)
- `active_only` (bool): Return only active supermarkets (default: false)

Response:
```json
[
  {
    "id": 1,
    "name": "Example Supermarket",
    "domain": "example.com",
    "base_url": "https://example.com",
    "spider_name": "example_spider",
    "scraping_config": {},
    "is_active": true,
    "created_at": "2023-01-01T00:00:00Z",
    "updated_at": "2023-01-01T00:00:00Z",
    "last_scraped_at": "2023-01-01T00:00:00Z"
  }
]
```

#### Get Supermarket Statistics
```
GET /api/v1/supermarkets/stats
```

Response:
```json
[
  {
    "id": 1,
    "name": "Example Supermarket",
    "total_products": 1500,
    "active_products": 1450,
    "total_discounts": 50,
    "active_discounts": 45,
    "last_scraped_at": "2023-01-01T00:00:00Z"
  }
]
```

#### Create Supermarket
```
POST /api/v1/supermarkets/
```

Request Body:
```json
{
  "name": "New Supermarket",
  "domain": "newsupermarket.com",
  "base_url": "https://newsupermarket.com",
  "spider_name": "new_supermarket",
  "scraping_config": {
    "download_delay": 2,
    "concurrent_requests": 1
  },
  "is_active": true
}
```

#### Update Supermarket
```
PUT /api/v1/supermarkets/{supermarket_id}
```

#### Delete Supermarket
```
DELETE /api/v1/supermarkets/{supermarket_id}?force=true
```

#### Trigger Scraping
```
POST /api/v1/supermarkets/{supermarket_id}/scrape
```

### Products

#### List Products
```
GET /api/v1/products/
```

Query Parameters:
- `skip` (int): Number of records to skip
- `limit` (int): Maximum number of records to return
- `search` (string): Search term for product name/description
- `supermarket_id` (int): Filter by supermarket
- `category_id` (int): Filter by category
- `brand` (string): Filter by brand
- `available_only` (bool): Return only available products

#### Compare Prices
```
GET /api/v1/products/compare?search=product_name
```

Response:
```json
[
  {
    "product_name": "Example Product",
    "brand": "Example Brand",
    "size": "500g",
    "prices": [
      {
        "supermarket": "Store A",
        "current_price": 4.99,
        "regular_price": 5.99,
        "discount_info": {
          "type": "percentage",
          "value": 16.7,
          "text": "Save 17%"
        }
      }
    ],
    "best_price": {
      "supermarket": "Store A",
      "current_price": 4.99
    },
    "price_range": {
      "min": 4.99,
      "max": 6.49,
      "currency": "USD"
    }
  }
]
```

#### Get Product Details
```
GET /api/v1/products/{product_id}
```

#### Get Price History
```
GET /api/v1/products/{product_id}/price-history?limit=30
```

#### List Discounts
```
GET /api/v1/products/discounts
```

Query Parameters:
- `supermarket_id` (int): Filter by supermarket
- `active_only` (bool): Return only active discounts

### Scraping

#### Get Scraping Logs
```
GET /api/v1/scraping/logs
```

Query Parameters:
- `supermarket_id` (int): Filter by supermarket
- `status` (string): Filter by status (started, completed, failed)
- `skip` (int): Number of records to skip
- `limit` (int): Maximum number of records to return

#### Get Scraping Statistics
```
GET /api/v1/scraping/stats
```

Response:
```json
{
  "total_runs": 150,
  "successful_runs": 140,
  "failed_runs": 10,
  "total_products_scraped": 15000,
  "total_errors": 25,
  "average_duration": 120.5,
  "last_run": "2023-01-01T00:00:00Z"
}
```

#### Start Scraping
```
POST /api/v1/scraping/start
```

Request Body (optional):
```json
{
  "supermarket_id": 1,
  "spider_name": "example_spider"
}
```

#### Stop Scraping
```
POST /api/v1/scraping/stop
```

Request Body (optional):
```json
{
  "task_id": "task-uuid",
  "spider_name": "example_spider"
}
```

#### Get Scraping Status
```
GET /api/v1/scraping/status
```

Response:
```json
{
  "active_tasks": 2,
  "pending_tasks": 0,
  "running_spiders": ["example_spider", "another_spider"],
  "system_status": "healthy"
}
```

## Error Responses

All endpoints return appropriate HTTP status codes and error messages:

### 400 Bad Request
```json
{
  "detail": "Invalid request parameters"
}
```

### 404 Not Found
```json
{
  "detail": "Resource not found"
}
```

### 422 Validation Error
```json
{
  "detail": [
    {
      "loc": ["body", "name"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### 500 Internal Server Error
```json
{
  "detail": "Internal server error"
}
```

## Rate Limiting

The API currently does not implement rate limiting, but it may be added in future versions.

## WebSocket Support

WebSocket endpoints for real-time updates may be added in future versions.

## SDK and Client Libraries

Currently, no official SDK is available. You can use any HTTP client library to interact with the API.

### Python Example
```python
import requests

# List supermarkets
response = requests.get("http://localhost:8000/api/v1/supermarkets/")
supermarkets = response.json()

# Start scraping
response = requests.post(
    "http://localhost:8000/api/v1/scraping/start",
    json={"supermarket_id": 1}
)
result = response.json()
```

### JavaScript Example
```javascript
// List products
fetch('/api/v1/products/')
  .then(response => response.json())
  .then(products => console.log(products));

// Compare prices
fetch('/api/v1/products/compare?search=milk')
  .then(response => response.json())
  .then(comparison => console.log(comparison));
```

## Interactive Documentation

Visit `http://localhost:8000/docs` for interactive API documentation powered by Swagger UI.

Alternative documentation is available at `http://localhost:8000/redoc`.
