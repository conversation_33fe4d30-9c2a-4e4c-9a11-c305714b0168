# Deployment Guide

This guide covers different deployment options for the Supermarket Price Comparison system.

## Quick Start (Local Development)

### Prerequisites
- Python 3.9+
- Redis server
- Git

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd supermarket-scraper

# Run the setup script
chmod +x scripts/start_local.sh
./scripts/start_local.sh
```

This will:
1. Create a virtual environment
2. Install dependencies
3. Set up the database
4. Start all services

Access the application at `http://localhost:8000`

## Local Network Deployment

### Prerequisites
- Docker and Docker Compose
- Network access to target devices

### Setup
```bash
# Run the deployment script
chmod +x scripts/deploy_local_network.sh
./scripts/deploy_local_network.sh
```

This will:
1. Configure CORS for local network access
2. Build Docker containers
3. Start all services
4. Display network access information

### Manual Docker Deployment

```bash
# Create production environment file
cp .env.example .env
# Edit .env with your configuration

# Build and start services
docker-compose up -d

# Run database migrations
docker-compose exec api alembic upgrade head

# Check status
docker-compose ps
```

## Production Deployment

### Option 1: Docker Compose (Recommended)

1. **Prepare the server:**
```bash
# Install Docker and Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

2. **Deploy the application:**
```bash
# Clone repository
git clone <repository-url>
cd supermarket-scraper

# Create production configuration
cp .env.example .env
# Edit .env for production settings

# Start services
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

3. **Set up reverse proxy (optional):**
```nginx
# /etc/nginx/sites-available/supermarket-scraper
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Option 2: Manual Installation

1. **System dependencies:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3.9 python3.9-venv python3-pip redis-server postgresql postgresql-contrib nginx

# CentOS/RHEL
sudo yum install python39 python39-pip redis postgresql postgresql-server nginx
```

2. **Application setup:**
```bash
# Create application user
sudo useradd -m -s /bin/bash scraper
sudo su - scraper

# Clone and setup
git clone <repository-url>
cd supermarket-scraper
python3.9 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env for production
```

3. **Database setup:**
```bash
# PostgreSQL
sudo -u postgres createuser scraper_user
sudo -u postgres createdb supermarket_scraper -O scraper_user
sudo -u postgres psql -c "ALTER USER scraper_user PASSWORD 'your_password';"

# Run migrations
alembic upgrade head
```

4. **Service configuration:**
```bash
# Create systemd services
sudo cp deployment/systemd/*.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable supermarket-api supermarket-worker supermarket-beat
sudo systemctl start supermarket-api supermarket-worker supermarket-beat
```

## Environment Configuration

### Development (.env)
```bash
DATABASE_URL=sqlite:///./supermarket_scraper.db
REDIS_URL=redis://localhost:6379/0
API_DEBUG=true
LOG_LEVEL=DEBUG
```

### Production (.env)
```bash
DATABASE_URL=postgresql://user:password@localhost:5432/supermarket_scraper
REDIS_URL=redis://localhost:6379/0
API_DEBUG=false
LOG_LEVEL=INFO
SECRET_KEY=your-secret-key-here
ALLOWED_ORIGINS=["https://yourdomain.com"]
```

## Security Considerations

### 1. Database Security
- Use strong passwords
- Limit database access to application only
- Enable SSL/TLS for database connections
- Regular backups

### 2. API Security
- Configure proper CORS origins
- Use HTTPS in production
- Implement rate limiting
- Add authentication if needed

### 3. Network Security
- Use firewall to limit access
- VPN for remote access
- Monitor access logs
- Regular security updates

### 4. Application Security
- Keep dependencies updated
- Use environment variables for secrets
- Implement proper logging
- Monitor for suspicious activity

## Monitoring and Maintenance

### 1. Health Checks
```bash
# Check API health
curl http://localhost:8000/health

# Check service status
docker-compose ps
# or
systemctl status supermarket-*
```

### 2. Log Monitoring
```bash
# Docker logs
docker-compose logs -f api
docker-compose logs -f celery-worker

# System logs
journalctl -u supermarket-api -f
tail -f logs/scraper.log
```

### 3. Performance Monitoring
- Monitor CPU and memory usage
- Check database performance
- Monitor scraping success rates
- Track response times

### 4. Backup Strategy
```bash
# Database backup
pg_dump supermarket_scraper > backup_$(date +%Y%m%d).sql

# Application backup
tar -czf app_backup_$(date +%Y%m%d).tar.gz /path/to/app
```

## Scaling

### Horizontal Scaling
1. **Multiple Workers:**
```yaml
# docker-compose.yml
celery-worker:
  # ... existing config
  deploy:
    replicas: 3
```

2. **Load Balancer:**
```yaml
nginx:
  image: nginx:alpine
  ports:
    - "80:80"
  volumes:
    - ./nginx.conf:/etc/nginx/nginx.conf
```

### Vertical Scaling
- Increase server resources
- Optimize database queries
- Tune Celery worker settings
- Implement caching

## Troubleshooting

### Common Issues

1. **Services won't start:**
```bash
# Check logs
docker-compose logs
# Check ports
netstat -tulpn | grep :8000
```

2. **Database connection errors:**
```bash
# Test connection
psql -h localhost -U scraper_user -d supermarket_scraper
# Check credentials in .env
```

3. **Scraping failures:**
```bash
# Test spider manually
cd scraper
scrapy crawl spider_name -L DEBUG
```

4. **High memory usage:**
```bash
# Monitor processes
htop
# Check Celery worker memory
docker stats
```

### Performance Optimization

1. **Database optimization:**
```sql
-- Add indexes
CREATE INDEX idx_product_name ON products(name);
CREATE INDEX idx_price_current ON prices(is_current);
```

2. **Celery optimization:**
```python
# celery_app.py
celery_app.conf.update(
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
)
```

3. **Caching:**
```python
# Add Redis caching for API responses
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
```

## Backup and Recovery

### Automated Backups
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump supermarket_scraper > /backups/db_$DATE.sql
tar -czf /backups/app_$DATE.tar.gz /app
# Upload to cloud storage
```

### Recovery Process
```bash
# Restore database
psql supermarket_scraper < backup.sql

# Restore application
tar -xzf app_backup.tar.gz -C /

# Restart services
docker-compose restart
```
