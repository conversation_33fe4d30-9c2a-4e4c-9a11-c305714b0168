# Adding New Supermarket Sites

This guide explains how to add new supermarket websites to the scraping system.

## Overview

Adding a new supermarket site involves:
1. Creating a new spider for the website
2. Configuring the spider in the database
3. Testing the spider
4. Deploying and monitoring

## Step 1: Create a New Spider

### 1.1 Spider File Structure

Create a new spider file in `scraper/supermarket_scraper/spiders/`:

```python
# scraper/supermarket_scraper/spiders/your_supermarket_spider.py

from .base import SupermarketSpider
from ..items import ProductItem, DiscountItem

class YourSupermarketSpider(SupermarketSpider):
    name = 'your_supermarket'
    supermarket_name = 'Your Supermarket Name'
    allowed_domains = ['yoursupermarket.com']
    start_urls = [
        'https://yoursupermarket.com/products',
    ]
    base_currency = "USD"  # or your local currency
    
    # Custom settings for this spider
    custom_settings = {
        'DOWNLOAD_DELAY': 2,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
    }
```

### 1.2 Required Methods

Implement these required methods in your spider:

#### parse(self, response)
Main parsing method to extract product links and handle pagination:

```python
def parse(self, response):
    # Extract product links
    product_links = response.css('.product-link::attr(href)').getall()
    for link in product_links:
        yield Request(url=urljoin(response.url, link), callback=self.parse_product_page)
    
    # Handle pagination
    next_page = response.css('.next-page::attr(href)').get()
    if next_page:
        yield Request(url=urljoin(response.url, next_page), callback=self.parse)
```

#### Extraction Methods
Implement these methods to extract specific data:

```python
def _extract_product_name(self, loader, response):
    name = response.css('h1.product-title::text').get()
    if name:
        loader.add_value('name', name.strip())

def _extract_product_prices(self, loader, response):
    current_price = response.css('.current-price::text').get()
    regular_price = response.css('.regular-price::text').get()
    if current_price:
        loader.add_value('current_price', current_price)
    if regular_price:
        loader.add_value('regular_price', regular_price)

# Implement all other required extraction methods...
```

### 1.3 CSS Selectors Guide

Common elements to look for:

| Data | Common Selectors |
|------|------------------|
| Product Name | `h1`, `.product-title`, `.product-name` |
| Price | `.price`, `.current-price`, `[data-price]` |
| Brand | `.brand`, `.product-brand`, `[data-brand]` |
| Description | `.description`, `.product-details` |
| Image | `.product-image img`, `.hero-image img` |
| Availability | `.in-stock`, `.out-of-stock`, `.availability` |
| Category | `.breadcrumb`, `.category` |

## Step 2: Configure in Database

### 2.1 Add Supermarket via API

Use the API to add your new supermarket:

```bash
curl -X POST "http://localhost:8000/api/v1/supermarkets/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Your Supermarket Name",
    "domain": "yoursupermarket.com",
    "base_url": "https://yoursupermarket.com",
    "spider_name": "your_supermarket",
    "is_active": true,
    "scraping_config": {
      "download_delay": 2,
      "concurrent_requests": 1
    }
  }'
```

### 2.2 Add via Web Interface

1. Open the web interface at `http://localhost:8000`
2. Navigate to "Supermarkets" section
3. Click "Add Supermarket"
4. Fill in the required information
5. Save the configuration

## Step 3: Testing Your Spider

### 3.1 Test Spider Syntax

```bash
cd scraper
scrapy check your_supermarket
```

### 3.2 Test Single Page

```bash
scrapy crawl your_supermarket -s CLOSESPIDER_PAGECOUNT=1 -L DEBUG
```

### 3.3 Test with Output

```bash
scrapy crawl your_supermarket -o test_output.json -s CLOSESPIDER_PAGECOUNT=5
```

### 3.4 Validate Output

Check the output file to ensure:
- Product names are extracted correctly
- Prices are in the right format
- All required fields are populated
- No duplicate items

## Step 4: Common Issues and Solutions

### 4.1 JavaScript-Heavy Sites

If the site uses JavaScript to load content:

```python
# Add to spider settings
custom_settings = {
    'DOWNLOADER_MIDDLEWARES': {
        'scrapy_selenium.SeleniumMiddleware': 800
    },
    'SELENIUM_DRIVER_NAME': 'chrome',
    'SELENIUM_DRIVER_ARGUMENTS': ['--headless']
}
```

### 4.2 Rate Limiting

If you get blocked or rate limited:

```python
custom_settings = {
    'DOWNLOAD_DELAY': 3,  # Increase delay
    'RANDOMIZE_DOWNLOAD_DELAY': True,
    'AUTOTHROTTLE_ENABLED': True,
    'AUTOTHROTTLE_START_DELAY': 1,
    'AUTOTHROTTLE_MAX_DELAY': 10,
}
```

### 4.3 Anti-Bot Protection

For sites with anti-bot protection:

```python
# Rotate user agents
custom_settings = {
    'DOWNLOADER_MIDDLEWARES': {
        'supermarket_scraper.middlewares.RotateUserAgentMiddleware': 400,
    }
}
```

### 4.4 CAPTCHA Handling

If the site uses CAPTCHAs:
- Implement CAPTCHA solving service integration
- Use residential proxies
- Reduce scraping frequency
- Consider API access instead

## Step 5: Best Practices

### 5.1 Respect robots.txt

Always check and respect the site's robots.txt:
```
https://yoursupermarket.com/robots.txt
```

### 5.2 Be Respectful

- Use appropriate delays between requests
- Don't overload the server
- Scrape during off-peak hours
- Monitor your impact

### 5.3 Handle Errors Gracefully

```python
def parse_product_page(self, response):
    try:
        product = self.parse_product(response)
        if product:
            yield product
    except Exception as e:
        self.logger.error(f"Error parsing {response.url}: {e}")
```

### 5.4 Data Quality

- Validate extracted data
- Handle missing fields gracefully
- Normalize price formats
- Clean text data

## Step 6: Monitoring and Maintenance

### 6.1 Monitor Spider Performance

Check the scraping logs regularly:
```bash
# View recent logs
curl "http://localhost:8000/api/v1/scraping/logs?supermarket_id=YOUR_ID&limit=10"
```

### 6.2 Update Selectors

Websites change frequently. Monitor for:
- Broken selectors
- Layout changes
- New anti-bot measures
- URL structure changes

### 6.3 Performance Optimization

- Profile your spider performance
- Optimize CSS selectors
- Reduce unnecessary requests
- Cache static data

## Example: Complete Spider Implementation

See `scraper/supermarket_scraper/spiders/example_supermarket.py` for a complete example implementation with all required methods.

## Troubleshooting

### Common Error Messages

| Error | Solution |
|-------|----------|
| `Spider not found` | Check spider name matches file name |
| `No items scraped` | Verify CSS selectors are correct |
| `Connection timeout` | Increase timeout settings |
| `403 Forbidden` | Check robots.txt, add delays |
| `Empty response` | Site might require JavaScript rendering |

### Debug Commands

```bash
# Test specific URL
scrapy shell "https://yoursupermarket.com/product/123"

# Debug with verbose logging
scrapy crawl your_supermarket -L DEBUG

# Profile spider performance
scrapy crawl your_supermarket -s STATS_CLASS=scrapy.statscollectors.MemoryStatsCollector
```

## Getting Help

If you encounter issues:
1. Check the logs for error messages
2. Test selectors in browser developer tools
3. Verify the site structure hasn't changed
4. Consult the Scrapy documentation
5. Check for similar implementations in existing spiders
