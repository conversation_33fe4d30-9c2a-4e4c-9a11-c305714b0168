// Supermarket Price Comparison - Main JavaScript

class SupermarketApp {
    constructor() {
        this.apiBase = '/api/v1';
        this.currentPage = 'dashboard';
        this.init();
    }

    async init() {
        this.setupNavigation();
        this.loadDashboard();
    }

    setupNavigation() {
        // Create navigation
        const nav = document.createElement('nav');
        nav.className = 'nav';
        nav.innerHTML = `
            <ul class="nav-list">
                <li><a href="#" class="nav-link active" data-page="dashboard">Dashboard</a></li>
                <li><a href="#" class="nav-link" data-page="supermarkets">Supermarkets</a></li>
                <li><a href="#" class="nav-link" data-page="products">Products</a></li>
                <li><a href="#" class="nav-link" data-page="compare">Price Compare</a></li>
                <li><a href="#" class="nav-link" data-page="deals">Best Deals</a></li>
                <li><a href="#" class="nav-link" data-page="scraping">Scraping</a></li>
            </ul>
        `;

        // Add navigation to header
        const header = document.querySelector('.header .container');
        if (header) {
            header.appendChild(nav);
        }

        // Setup navigation event listeners
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('nav-link')) {
                e.preventDefault();
                const page = e.target.dataset.page;
                this.navigateTo(page);
            }
        });
    }

    navigateTo(page) {
        // Update active nav link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-page="${page}"]`).classList.add('active');

        this.currentPage = page;

        // Load page content
        switch (page) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'supermarkets':
                this.loadSupermarkets();
                break;
            case 'products':
                this.loadProducts();
                break;
            case 'compare':
                this.loadPriceComparison();
                break;
            case 'deals':
                this.loadBestDeals();
                break;
            case 'scraping':
                this.loadScrapingStatus();
                break;
        }
    }

    async loadDashboard() {
        const app = document.getElementById('app');
        app.innerHTML = `
            <div class="container">
                <div class="header">
                    <h1>Supermarket Price Comparison Dashboard</h1>
                </div>
                
                <div class="grid grid-cols-4 mb-4">
                    <div class="card">
                        <h3 class="card-title">Total Supermarkets</h3>
                        <p class="text-2xl font-bold text-primary" id="total-supermarkets">-</p>
                    </div>
                    <div class="card">
                        <h3 class="card-title">Total Products</h3>
                        <p class="text-2xl font-bold text-success" id="total-products">-</p>
                    </div>
                    <div class="card">
                        <h3 class="card-title">Active Deals</h3>
                        <p class="text-2xl font-bold text-warning" id="active-deals">-</p>
                    </div>
                    <div class="card">
                        <h3 class="card-title">Last Scrape</h3>
                        <p class="text-sm text-gray-600" id="last-scrape">-</p>
                    </div>
                </div>

                <div class="grid grid-cols-2">
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">Recent Scraping Activity</h2>
                        </div>
                        <div id="recent-scraping">Loading...</div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">Top Deals</h2>
                        </div>
                        <div id="top-deals">Loading...</div>
                    </div>
                </div>
            </div>
        `;

        // Load dashboard data
        await this.loadDashboardData();
    }

    async loadDashboardData() {
        try {
            // Load supermarket stats
            const supermarketStats = await this.apiCall('/supermarkets/stats');
            document.getElementById('total-supermarkets').textContent = supermarketStats.length;

            // Load scraping stats
            const scrapingStats = await this.apiCall('/scraping/stats');
            document.getElementById('last-scrape').textContent = 
                scrapingStats.last_run ? new Date(scrapingStats.last_run).toLocaleString() : 'Never';

            // Load recent scraping logs
            const recentLogs = await this.apiCall('/scraping/logs?limit=5');
            this.renderRecentScraping(recentLogs);

            // Load top deals
            const topDeals = await this.apiCall('/products/discounts?limit=5');
            this.renderTopDeals(topDeals);

        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    async loadSupermarkets() {
        const app = document.getElementById('app');
        app.innerHTML = `
            <div class="container">
                <div class="flex justify-between items-center mb-4">
                    <h1>Supermarket Management</h1>
                    <button class="btn btn-primary" onclick="app.showAddSupermarketModal()">
                        Add Supermarket
                    </button>
                </div>
                
                <div class="card">
                    <div id="supermarkets-table">Loading...</div>
                </div>
            </div>
        `;

        await this.loadSupermarketsTable();
    }

    async loadSupermarketsTable() {
        try {
            const supermarkets = await this.apiCall('/supermarkets');
            const tableContainer = document.getElementById('supermarkets-table');
            
            if (supermarkets.length === 0) {
                tableContainer.innerHTML = '<p class="text-center text-gray-500">No supermarkets configured yet.</p>';
                return;
            }

            const table = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Domain</th>
                            <th>Spider</th>
                            <th>Status</th>
                            <th>Last Scraped</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${supermarkets.map(sm => `
                            <tr>
                                <td class="font-semibold">${sm.name}</td>
                                <td>${sm.domain}</td>
                                <td><code class="text-sm">${sm.spider_name}</code></td>
                                <td>
                                    <span class="badge ${sm.is_active ? 'badge-success' : 'badge-danger'}">
                                        ${sm.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </td>
                                <td class="text-sm text-gray-600">
                                    ${sm.last_scraped_at ? new Date(sm.last_scraped_at).toLocaleString() : 'Never'}
                                </td>
                                <td>
                                    <div class="flex gap-2">
                                        <button class="btn btn-sm btn-primary" onclick="app.startScraping(${sm.id})">
                                            Scrape
                                        </button>
                                        <button class="btn btn-sm btn-secondary" onclick="app.editSupermarket(${sm.id})">
                                            Edit
                                        </button>
                                        <button class="btn btn-sm btn-danger" onclick="app.deleteSupermarket(${sm.id})">
                                            Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            tableContainer.innerHTML = table;
        } catch (error) {
            console.error('Error loading supermarkets:', error);
            document.getElementById('supermarkets-table').innerHTML = 
                '<p class="text-danger">Error loading supermarkets. Please try again.</p>';
        }
    }

    async loadProducts() {
        const app = document.getElementById('app');
        app.innerHTML = `
            <div class="container">
                <div class="flex justify-between items-center mb-4">
                    <h1>Product Catalog</h1>
                    <div class="flex gap-2">
                        <input type="text" class="form-input" placeholder="Search products..." id="product-search">
                        <button class="btn btn-primary" onclick="app.searchProducts()">Search</button>
                    </div>
                </div>

                <div class="card">
                    <div id="products-table">Loading...</div>
                </div>
            </div>
        `;

        await this.loadProductsTable();
    }

    async loadPriceComparison() {
        const app = document.getElementById('app');
        app.innerHTML = `
            <div class="container">
                <div class="flex justify-between items-center mb-4">
                    <h1>Price Comparison</h1>
                    <div class="flex gap-2">
                        <input type="text" class="form-input" placeholder="Search product to compare..." id="compare-search">
                        <button class="btn btn-primary" onclick="app.searchComparison()">Compare Prices</button>
                    </div>
                </div>

                <div class="card">
                    <div id="comparison-results">Enter a product name to compare prices across supermarkets.</div>
                </div>
            </div>
        `;
    }

    async loadBestDeals() {
        const app = document.getElementById('app');
        app.innerHTML = `
            <div class="container">
                <h1>Best Deals</h1>

                <div class="card">
                    <div id="deals-table">Loading...</div>
                </div>
            </div>
        `;

        await this.loadDealsTable();
    }

    async loadScrapingStatus() {
        const app = document.getElementById('app');
        app.innerHTML = `
            <div class="container">
                <h1>Scraping Status</h1>

                <div class="grid grid-cols-2 mb-4">
                    <div class="card">
                        <h3 class="card-title">Current Status</h3>
                        <div id="scraping-status">Loading...</div>
                    </div>
                    <div class="card">
                        <h3 class="card-title">Recent Logs</h3>
                        <div id="scraping-logs">Loading...</div>
                    </div>
                </div>
            </div>
        `;

        await this.loadScrapingData();
    }

    async loadProductsTable(search = '') {
        try {
            const url = search ? `/products?search=${encodeURIComponent(search)}` : '/products?limit=50';
            const products = await this.apiCall(url);
            const tableContainer = document.getElementById('products-table');
            
            if (products.length === 0) {
                tableContainer.innerHTML = '<p class="text-center text-gray-500">No products found.</p>';
                return;
            }

            const table = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Brand</th>
                            <th>Supermarket</th>
                            <th>Price</th>
                            <th>Status</th>
                            <th>Last Updated</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${products.map(product => `
                            <tr>
                                <td>
                                    <div class="font-semibold">${product.name}</div>
                                    ${product.size ? `<div class="text-sm text-gray-500">${product.size}</div>` : ''}
                                </td>
                                <td>${product.brand || '-'}</td>
                                <td>${product.supermarket_name}</td>
                                <td>
                                    ${product.current_price ? 
                                        `<span class="font-semibold">$${product.current_price.toFixed(2)}</span>` : 
                                        '<span class="text-gray-400">No price</span>'
                                    }
                                </td>
                                <td>
                                    <span class="badge ${product.is_available ? 'badge-success' : 'badge-danger'}">
                                        ${product.is_available ? 'Available' : 'Out of Stock'}
                                    </span>
                                </td>
                                <td class="text-sm text-gray-600">
                                    ${new Date(product.last_seen_at).toLocaleString()}
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            tableContainer.innerHTML = table;
        } catch (error) {
            console.error('Error loading products:', error);
            document.getElementById('products-table').innerHTML = 
                '<p class="text-danger">Error loading products. Please try again.</p>';
        }
    }

    async apiCall(endpoint, options = {}) {
        const url = this.apiBase + endpoint;
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`API call failed: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    renderRecentScraping(logs) {
        const container = document.getElementById('recent-scraping');
        if (logs.length === 0) {
            container.innerHTML = '<p class="text-gray-500">No recent scraping activity.</p>';
            return;
        }

        const html = logs.map(log => `
            <div class="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                <div>
                    <div class="font-semibold">${log.supermarket_name}</div>
                    <div class="text-sm text-gray-500">${new Date(log.started_at).toLocaleString()}</div>
                </div>
                <span class="badge ${log.status === 'completed' ? 'badge-success' : 
                                   log.status === 'failed' ? 'badge-danger' : 'badge-warning'}">
                    ${log.status}
                </span>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    renderTopDeals(deals) {
        const container = document.getElementById('top-deals');
        if (deals.length === 0) {
            container.innerHTML = '<p class="text-gray-500">No active deals found.</p>';
            return;
        }

        const html = deals.map(deal => `
            <div class="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                <div>
                    <div class="font-semibold">${deal.product_name}</div>
                    <div class="text-sm text-gray-500">${deal.supermarket_name}</div>
                </div>
                <div class="text-right">
                    <div class="font-semibold text-success">$${deal.discounted_price.toFixed(2)}</div>
                    <div class="text-sm text-gray-500 line-through">$${deal.original_price.toFixed(2)}</div>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    async startScraping(supermarketId) {
        try {
            const result = await this.apiCall(`/supermarkets/${supermarketId}/scrape`, {
                method: 'POST'
            });
            alert(`Scraping started: ${result.message}`);
        } catch (error) {
            alert(`Error starting scraping: ${error.message}`);
        }
    }

    searchProducts() {
        const searchTerm = document.getElementById('product-search').value;
        this.loadProductsTable(searchTerm);
    }

    async searchComparison() {
        const searchTerm = document.getElementById('compare-search').value;
        if (!searchTerm) {
            alert('Please enter a product name to compare');
            return;
        }

        try {
            const results = await this.apiCall(`/products/compare?search=${encodeURIComponent(searchTerm)}`);
            const container = document.getElementById('comparison-results');

            if (results.length === 0) {
                container.innerHTML = '<p class="text-gray-500">No products found for comparison.</p>';
                return;
            }

            const html = results.map(comparison => `
                <div class="card mb-4">
                    <h3 class="font-semibold">${comparison.product_name}</h3>
                    ${comparison.brand ? `<p class="text-sm text-gray-600">${comparison.brand}</p>` : ''}
                    <div class="mt-2">
                        ${comparison.prices.map(price => `
                            <div class="flex justify-between items-center py-2 border-b">
                                <span>${price.supermarket}</span>
                                <span class="font-semibold">$${price.current_price.toFixed(2)}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        } catch (error) {
            console.error('Error comparing prices:', error);
            document.getElementById('comparison-results').innerHTML =
                '<p class="text-danger">Error loading price comparison. Please try again.</p>';
        }
    }

    async loadDealsTable() {
        try {
            const deals = await this.apiCall('/products/discounts?limit=20');
            const tableContainer = document.getElementById('deals-table');

            if (deals.length === 0) {
                tableContainer.innerHTML = '<p class="text-center text-gray-500">No active deals found.</p>';
                return;
            }

            const table = `
                <table class="table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Supermarket</th>
                            <th>Original Price</th>
                            <th>Discounted Price</th>
                            <th>Savings</th>
                            <th>Promotion</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${deals.map(deal => `
                            <tr>
                                <td class="font-semibold">${deal.product_name}</td>
                                <td>${deal.supermarket_name}</td>
                                <td class="line-through text-gray-500">$${deal.original_price.toFixed(2)}</td>
                                <td class="font-semibold text-success">$${deal.discounted_price.toFixed(2)}</td>
                                <td class="text-success">$${(deal.original_price - deal.discounted_price).toFixed(2)}</td>
                                <td class="text-sm">${deal.promotion_text || '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            tableContainer.innerHTML = table;
        } catch (error) {
            console.error('Error loading deals:', error);
            document.getElementById('deals-table').innerHTML =
                '<p class="text-danger">Error loading deals. Please try again.</p>';
        }
    }

    async loadScrapingData() {
        try {
            const status = await this.apiCall('/scraping/status');
            const logs = await this.apiCall('/scraping/logs?limit=10');

            // Update status
            const statusContainer = document.getElementById('scraping-status');
            statusContainer.innerHTML = `
                <div class="space-y-2">
                    <div><strong>Active Tasks:</strong> ${status.active_tasks}</div>
                    <div><strong>System Status:</strong>
                        <span class="badge ${status.system_status === 'healthy' ? 'badge-success' : 'badge-warning'}">
                            ${status.system_status}
                        </span>
                    </div>
                    <div><strong>Running Spiders:</strong> ${status.running_spiders.join(', ') || 'None'}</div>
                </div>
            `;

            // Update logs
            const logsContainer = document.getElementById('scraping-logs');
            if (logs.length === 0) {
                logsContainer.innerHTML = '<p class="text-gray-500">No recent scraping logs.</p>';
            } else {
                const html = logs.map(log => `
                    <div class="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
                        <div>
                            <div class="font-semibold">${log.supermarket_name}</div>
                            <div class="text-sm text-gray-500">${new Date(log.started_at).toLocaleString()}</div>
                        </div>
                        <span class="badge ${log.status === 'completed' ? 'badge-success' :
                                           log.status === 'failed' ? 'badge-danger' : 'badge-warning'}">
                            ${log.status}
                        </span>
                    </div>
                `).join('');
                logsContainer.innerHTML = html;
            }
        } catch (error) {
            console.error('Error loading scraping data:', error);
        }
    }

    showAddSupermarketModal() {
        // Create modal HTML
        const modalHTML = `
            <div id="supermarket-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg p-6 w-full max-w-md">
                    <h2 class="text-xl font-bold mb-4">Add New Supermarket</h2>
                    <form id="supermarket-form">
                        <div class="form-group">
                            <label class="form-label">Name</label>
                            <input type="text" id="sm-name" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Domain</label>
                            <input type="text" id="sm-domain" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Base URL</label>
                            <input type="url" id="sm-base-url" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Spider Name</label>
                            <input type="text" id="sm-spider-name" class="form-input" required>
                        </div>
                        <div class="flex gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">Add Supermarket</button>
                            <button type="button" class="btn btn-secondary" onclick="app.closeModal()">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add form submit handler
        document.getElementById('supermarket-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitSupermarketForm();
        });
    }

    closeModal() {
        const modal = document.getElementById('supermarket-modal');
        if (modal) {
            modal.remove();
        }
    }

    async submitSupermarketForm() {
        const formData = {
            name: document.getElementById('sm-name').value,
            domain: document.getElementById('sm-domain').value,
            base_url: document.getElementById('sm-base-url').value,
            spider_name: document.getElementById('sm-spider-name').value,
            is_active: true
        };

        try {
            const result = await this.apiCall('/supermarkets/', {
                method: 'POST',
                body: JSON.stringify(formData)
            });

            alert('Supermarket added successfully!');
            this.closeModal();
            this.loadSupermarkets(); // Refresh the list
        } catch (error) {
            console.error('Error adding supermarket:', error);
            alert('Error adding supermarket. Please try again.');
        }
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new SupermarketApp();
});
