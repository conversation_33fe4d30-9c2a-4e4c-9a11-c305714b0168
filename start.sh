#!/bin/bash

# One-command startup script for Supermarket Price Comparison Scraper
# Usage: ./start.sh

set -e

echo "🚀 Starting Supermarket Price Comparison Scraper with Docker Compose..."

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose >/dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Get local IP address for network access
LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "localhost")
echo "📍 Local IP detected: $LOCAL_IP"

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs data

# Start Docker Compose
echo "🐳 Starting Docker containers..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 20

# Check if API is responding
echo "🔍 Checking API health..."
for i in {1..30}; do
    if curl -f http://localhost:8000/health >/dev/null 2>&1; then
        echo "✅ API service is healthy"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ API service failed to start. Checking logs..."
        docker-compose logs api
        exit 1
    fi
    echo "⏳ Waiting for API service... ($i/30)"
    sleep 3
done

# Display service status
echo ""
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "🌐 Access the application:"
echo "   • Web Interface: http://localhost:8000"
echo "   • Network Access: http://${LOCAL_IP}:8000"
echo "   • API Documentation: http://localhost:8000/docs"
echo "   • Flower Monitoring: http://localhost:5555"
echo ""
echo "📱 For network access from other devices:"
echo "   Use: http://${LOCAL_IP}:8000"
echo ""
echo "🔧 Useful commands:"
echo "   • View logs: docker-compose logs -f"
echo "   • Stop: docker-compose down"
echo "   • Restart: docker-compose restart"
echo ""
echo "📚 Next steps:"
echo "   1. Open http://localhost:8000 in your browser"
echo "   2. Go to 'Supermarkets' to add your first supermarket site"
echo "   3. Check docs/adding-sites.md for detailed instructions"
echo ""
