# Docker Compose Setup

## One-Command Deployment

Simply run:

```bash
docker compose up -d
```

That's it! The entire application will be built and started automatically.

## What Happens

When you run `docker compose up -d`, the system will:

1. **Build the application** (first time only)
2. **Start PostgreSQL database** with health checks
3. **Start Redis** for task queue
4. **Start the main API** with automatic database migration
5. **Start Celery worker** for background scraping tasks
6. **Start Celery beat** for scheduled tasks
7. **Start Flower** for monitoring

## Access Points

After startup (usually takes 30-60 seconds):

- **Web Interface**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Flower Monitoring**: http://localhost:5555
- **Health Check**: http://localhost:8000/health

## Network Access

The application is automatically configured for local network access. Other devices on your network can access it using your computer's IP address:

```
http://YOUR_LOCAL_IP:8000
```

To find your local IP:
```bash
# On macOS/Linux
hostname -I | awk '{print $1}'

# On Windows
ipconfig | findstr "IPv4"
```

## Management Commands

```bash
# View logs
docker compose logs -f

# View logs for specific service
docker compose logs -f api

# Stop all services
docker compose down

# Restart services
docker compose restart

# Rebuild and restart
docker compose up -d --build

# Check service status
docker compose ps
```

## Troubleshooting

### Services won't start
```bash
# Check logs
docker compose logs

# Check individual service
docker compose logs api
```

### Database issues
```bash
# Reset database
docker compose down
docker volume rm scraping_postgres_data
docker compose up -d
```

### Build issues on Apple Silicon
The Dockerfile automatically detects ARM64 architecture and skips Chrome installation (which is optional).

### Port conflicts
If ports 8000 or 5555 are already in use:
```bash
# Check what's using the port
lsof -i :8000

# Stop the conflicting service or modify docker-compose.yml
```

## Environment Variables

The system uses these default settings in Docker:
- Database: PostgreSQL (automatic setup)
- Redis: Default configuration
- CORS: Configured for local network access
- Scraping: Respectful defaults (2s delay, 8 concurrent requests)

To customize, create a `.env` file or modify the environment variables in `docker-compose.yml`.

## Data Persistence

Your data is automatically persisted in Docker volumes:
- `postgres_data`: Database data
- `redis_data`: Redis data
- `./logs`: Application logs (mounted from host)
- `./data`: Application data (mounted from host)

## First Steps

1. Wait for all services to start (check with `docker compose ps`)
2. Open http://localhost:8000
3. Go to "Supermarkets" section
4. Add your first supermarket site
5. Check the documentation at `docs/adding-sites.md`

## Production Notes

For production deployment:
- Change the SECRET_KEY in docker-compose.yml
- Set up proper SSL/TLS termination
- Configure proper backup for volumes
- Monitor resource usage
- Set up log rotation
