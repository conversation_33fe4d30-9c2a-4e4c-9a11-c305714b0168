# Core scraping framework
scrapy==2.11.0
scrapy-splash==0.8.0
scrapy-selenium==0.0.7

# Database and ORM
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Web framework for API and interface
fastapi==0.104.1
uvicorn==0.24.0
jinja2==3.1.2
python-multipart==0.0.6

# Data processing and analysis
pandas==2.1.3
numpy==1.25.2
python-dateutil==2.8.2

# HTTP requests and parsing
requests==2.31.0
beautifulsoup4==4.12.2
lxml==4.9.3
selenium==4.15.2

# Scheduling and background tasks
celery==5.3.4
redis==5.0.1
apscheduler==3.10.4

# Configuration and environment
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Logging and monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# CORS and security (built into FastAPI)
# python-jose[cryptography]==3.3.0
